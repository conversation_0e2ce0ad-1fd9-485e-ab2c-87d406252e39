@extends($activeTemplate.'layouts.master')
@section('content')
<div class="dashboard-inner">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card custom--card">
                <div class="card-header">
                    <h5 class="text-center"> <i class="las la-wallet"></i> @lang('Zynte Payment')</h5>
                </div>
                <div class="card-body p-5">
                    <div class="text-center">
                        <ul class="list-group text-center">
                            <li class="list-group-item d-flex justify-content-between">
                                @lang('You have to pay '):
                                <strong>{{showAmount($deposit->final_amo)}} {{__($deposit->method_currency)}}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                @lang('You will get '):
                                <strong>{{showAmount($deposit->amount)}}  {{__($general->cur_text)}}</strong>
                            </li>
                        </ul>
                        
                        <div class="mt-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">@lang('Loading...')</span>
                            </div>
                            <p class="mt-2">@lang('Redirecting to Zynte Payment Gateway...')</p>
                            <p class="text-muted small">@lang('Please wait while we redirect you to complete your payment')</p>
                        </div>
                        
                        <div class="mt-3">
                            <a href="{{route('user.deposit.index')}}" class="btn btn-secondary">@lang('Cancel Payment')</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto redirect to payment URL when page loads
    window.addEventListener('load', function() {
        // This will be handled by the ProcessController redirect
        // The redirect happens automatically from the backend
    });
</script>
@endsection
