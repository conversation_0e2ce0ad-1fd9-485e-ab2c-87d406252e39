 @extends($activeTemplate . 'layouts.master')
@section('content')
 
  <div id="app" data-v-app="" class="a-t-5 no-1">

                    <!--<div data-v-f09a6d59="" data-v-a652ce47="" class="navigation">-->
                    <!--    <div data-v-f09a6d59="" class="navigation-content">-->
                    <!--        <div data-v-f09a6d59="" class="h-full flex cursor-pointer items-center justify-between">-->
                    <!--            <div data-v-f09a6d59="" class="icon i-material-symbols-arrow-back-ios-new-rounded"></div>-->
                    <!--            <div data-v-f09a6d59=""></div>-->
                    <!--            <div data-v-f09a6d59="" class="opacity-0">h</div>-->
                    <!--        </div>-->
                    <!--    </div>-->
                    <!--</div>-->
                                        
                        <style>
        select option{
            background:#fff;
            color:#000;
        }
    </style>
    <div data-v-7aa53151="" class="withdraw-wrap p-$mg">
    <div data-v-7aa53151="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text">
        <div data-v-7aa53151="" class=":uno: flex items-center">
            <div data-v-7aa53151="" class="mr-3">
                <div data-v-7aa53151="" class=":uno: base-logo flex items-center small-logo">
                    <img class="site-img h-full w-full rd-50%" src="{{ asset(getImage(getFilePath('logoIcon') . '/logo_2.png')) }}" draggable="false" alt="logo" />
                    <!---->
                </div>
            </div>
            <div data-v-7aa53151="" class="shrink-0">
                <div data-v-7aa53151="" class=":uno: text-left text-13px lh-20px c-red">24-hour withdrawal</div>
            </div>
            <a data-v-7aa53151="" href="" class="ml-auto shrink-0 text-xl">
                <div data-v-7aa53151="" class="i-bx:bxs-food-menu text-$primary"></div>
            </a>
        </div>
        <div data-v-7aa53151="" class="number">
            <div data-v-7aa53151="" class="title">Withdrawable</div>
            <div data-v-7aa53151="" class="num">{{ showAmount(auth()->user()->interest_wallet) }}<span data-v-7aa53151="">{{ $general->cur_text }}</span></div>
        </div>
        <div data-v-7aa53151="" class="pay-type">
            <div data-v-7aa53151="" class="shrink-0">Payment method</div>
            <div data-v-7aa53151="" class="van-radio-group" role="radiogroup" style="margin-left: 10px;">
                <div data-v-7aa53151="" role="radio" class="van-radio" tabindex="0" aria-checked="true">
                    <div class="van-radio__icon van-radio__icon--round van-radio__icon--checked">
                        <i class="van-badge__wrapper van-icon van-icon-success" style="border-color: var(--primary); background-color: var(--primary);"><!----><!----><!----></i>
                    </div>
                    <span class="van-radio__label"> {{ $general->cur_text }} </span>
                </div>
            </div>
        </div>
        
   <form action="{{route('user.withdraw.money')}}" method="post">
    @csrf         <input type="hidden" name="method_code" value="1"> 
                                          
            <div data-v-7aa53151="" class="base-input is-number">
                <div class="input-box">
                    <div class="input-left-slot"></div>
              
                    
                        <select class="uni-input-input w-full" style="background:transparent;" name="method_code" >
                                  <option>Select Bank</option>
                            
        @foreach($withdrawMethod as $data)
            <option value="{{ $data->id }}" data-resource="{{$data}}">  {{__($data->name)}}</option>
        @endforeach
    </select>
                    
                 
                    <!--<input type="text" placeholder="Bank Name" name="bank" class="w-full" />-->
                </div>
            </div>
      
            
            <div class="grid grid-cols-3 gap-3 mt-20px">
        <a href="{{route ('user.deposit.index')}}" class="w-full h-108px pt-7px box-border bg-[rgba(243,243,243,0.1)] rounded-18px">
            <div class="flex justify-center"><img class="w-78px h-66px" src="/profelar/wi.png" /></div>
            <div class="text-center mt-7px">Recharge</div>
        </a>
        <a href="{{route ('user.withdraw')}}" class="w-full h-108px pt-7px box-border bg-[rgba(243,243,243,0.1)] rounded-18px">
            <div class="flex justify-center"><img class="w-78px h-66px" src="/profelar/ref.png" /></div>
            <div class="text-center mt-7px">Team</div>
        </a>
        <a href="{{ route('user.transactions') }}" class="w-full h-108px pt-7px box-border bg-[rgba(243,243,243,0.1)] rounded-18px">
            <div class="flex justify-center"><img class="w-78px h-66px" src="/profelar/note.png" /></div>
            <div class="text-center mt-7px">Detail</div>
        </a>
    </div>
            
            <div data-v-7aa53151="" class="base-input is-number">
                <div class="input-box">
                    <div class="input-left-slot"></div>
                    <input type="text" placeholder="Withdrawal Amount" name="amount" class="w-full" />
                </div>
            </div>
            
            <div data-v-7aa53151="" class="mt-10px flex items-center justify-between">
                <span data-v-7aa53151="" class="text-sm text-$text-gray">Handling Fee</span>
                <div data-v-7aa53151="" class="text-sm text-$text-gray text-right">
                    <div data-v-7aa53151="" class="line-through">0 {{ $general->cur_text }}</div>
                    <div data-v-7aa53151="" class="text-$text-gray text-xs">Number of remaining fee-include</div>
                </div>
            </div>
            <div data-v-7aa53151="" class="mt-10px flex items-center justify-between">
                <span data-v-7aa53151="" class="text-sm text-$text-gray">Receipt</span>
                <div data-v-7aa53151="" class="text-sm text-green">0 {{ $general->cur_text }}</div>
            </div>
            <button type="submit" data-v-7aa53151="" class=":uno: base-main-btn flex items-center justify-center">
                <div class="base-main-btn-content">
                    <!---->
                    <span data-v-7aa53151="">Confirm</span>
                </div>
            </button>
        </form>
    </div>
</div>
<img src="/profelar/pay.jpg" style="border: none; width: 100%;" alt="PROFELAR" />

@endsection
    
    