<?php

namespace App\Http\Controllers\Gateway\Zynte;

use App\Models\Deposit;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Gateway\PaymentController;
use Illuminate\Http\Request;

class ProcessController extends Controller
{
    /*
     * Zynte Gateway
     */

    public static function process($deposit)
    {
        $zynteAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);
        $user = auth()->user();
        $general = gs();

        // Ensure all required data is available
        $userEmail = $user->email;
        $userName = $user->username;
        $siteName = $general->site_name;

        // Validate critical user data
        if (empty($userEmail)) {
            $send['error'] = true;
            $send['message'] = 'User email is required for payment processing. Please update your profile.';
            return json_encode($send);
        }

        if (empty($userName)) {
            $send['error'] = true;
            $send['message'] = 'Username is required for payment processing. Please update your profile.';
            return json_encode($send);
        }

        // Prepare data for Zynte API
        $data = [
            'customer_mobile' => $user->mobile ?? '9999999999',
            'merch_id' => $zynteAcc->merchant_id,
            'amount' => (int)round($deposit->final_amo, 2),
            'order_id' => $deposit->trx,
            'currency' => $deposit->method_currency,
            'redirect_url' => route(gatewayRedirectUrl(true)),
            'udf1' => $userName,
            'udf2' => $userEmail,
            'udf3' => 'Investment Deposit',
            'udf4' => $siteName ?? 'Investment Platform',
            'udf5' => (string)$deposit->id,
        ];

        // Generate X-VERIFY header
        $xverify = self::generateXVerify($data, $zynteAcc->secret_key);

        // Log request for debugging (remove in production)
        \Log::info('Zynte API Request', [
            'data' => $data,
            'xverify' => $xverify
        ]);

        // Make API call to Zynte
        $response = self::makeApiCall('https://zynte.in/api/v1/order', $data, $xverify);

        if ($response === false) {
            $send['error'] = true;
            $send['message'] = 'Failed to connect to Zynte API';
            return json_encode($send);
        }

        // Log response for debugging (remove in production)
        \Log::info('Zynte API Response', ['response' => $response]);

        $responseData = json_decode($response, true);

        if (isset($responseData['status']) && $responseData['status'] === true) {
            // Store payment details for later verification
            $deposit->btc_wallet = $responseData['result']['orderId'];
            $deposit->save();

            $send['redirect'] = true;
            $send['redirect_url'] = $responseData['result']['payment_url'];
        } else {
            $send['error'] = true;
            $send['message'] = $responseData['message'] ?? 'Payment creation failed';

            // Log error for debugging
            \Log::error('Zynte Payment Error', [
                'response' => $responseData,
                'request_data' => $data
            ]);
        }

        return json_encode($send);
    }

    public function ipn(Request $request)
    {
        // Handle Zynte webhook
        $status = $request->input('status');
        $order_id = $request->input('order_id');
        $amount = $request->input('amount');
        $currency_code = $request->input('currency_code');

        $deposit = Deposit::where('trx', $order_id)->where('status', 0)->first();

        if (!$deposit) {
            return response('Order not found', 404);
        }

        // Verify the payment with Zynte API
        $zynteAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);
        $verificationData = [
            'order_id' => $order_id,
            'merch_id' => $zynteAcc->merchant_id,
        ];

        $xverify = self::generateXVerify($verificationData, $zynteAcc->secret_key);
        $response = self::makeApiCall('https://zynte.in/api/v1/status', $verificationData, $xverify);

        if ($response !== false) {
            $responseData = json_decode($response, true);
            
            if (isset($responseData['status']) && $responseData['status'] === true) {
                $txnStatus = $responseData['result']['txnStatus'] ?? '';
                
                if ($txnStatus === 'SUCCESS' && $status === 'SUCCESS') {
                    // Verify amount matches
                    if (round($deposit->final_amo, 2) == round($amount, 2)) {
                        PaymentController::userDataUpdate($deposit);
                        return response('Payment verified successfully', 200);
                    }
                }
            }
        }

        return response('Payment verification failed', 400);
    }

    /**
     * Generate X-VERIFY header for Zynte API
     */
    private static function generateXVerify($data, $secret_key)
    {
        ksort($data);
        $dataString = implode('|', array_map(function ($key, $value) {
            return $key . '=' . $value;
        }, array_keys($data), $data));
        
        return hash_hmac('sha256', $dataString, $secret_key);
    }

    /**
     * Make API call to Zynte
     */
    private static function makeApiCall($url, $data, $xverify)
    {
        $headers = [
            'Content-Type: application/json',
            'X-VERIFY: ' . $xverify,
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            \Log::error('Zynte cURL Error', [
                'error' => $error,
                'url' => $url,
                'data' => $data
            ]);
            curl_close($ch);
            return false;
        }

        // Log HTTP response code for debugging
        \Log::info('Zynte API HTTP Response', [
            'http_code' => $httpCode,
            'url' => $url
        ]);

        curl_close($ch);
        return $response;
    }
}
