     @extends($activeTemplate . 'layouts.master')
@section('content')
     
        <div id="app" data-v-app="" class="a-t-5 no-1">
            <div class="van-config-provider">
                <div data-v-a652ce47="" class="box-border min-h-full w-full layout-tab-bar px-$mg pt-45px">
                                        <div data-v-a652ce47="" class="nav-bar-wrap" style="--71594674: 0;margin-top: -25px;">
                        <div class="nav-bar">
                            
                            <div data-v-fcd9c982="" class="navbar-wrap">
                                <div data-v-fcd9c982="" class="w-full px-$mg flex justify-between items-center">
                                    <div data-v-fcd9c982="" class="app-info-wrap">
                                        <div data-v-fcd9c982="" class="w-88px h-auto max-h-88px"><img data-v-fcd9c982="" class="w-full h-full" src="{{ asset(getImage(getFilePath('logoIcon') . '/logo_2.png')) }}" /></div>

                                        <div data-v-fcd9c982="" class="font-bold text-bas ml-4px"></div>
                                    </div>
                                    <div data-v-fcd9c982="" class="tools-wrap">
                                        <div data-v-fcd9c982="" class="w-30px h-30px bg-#fff bg-opacity-20 rounded-full flex items-center justify-center">
                                            <svg data-v-fcd9c982="" class="svg-icon 5-lang w-20px h-20px w-20px h-20px" aria-hidden="true">
                                                <use xlink:href="#svg-5-lang"></use>
                                            </svg>
                                        </div>
                                        <div data-v-fcd9c982="" class="ml-16px w-30px h-30px bg-#fff bg-opacity-20 rounded-full flex items-center justify-center">
                                            <svg data-v-fcd9c982="" class="svg-icon 5-message w-16px h-16px w-16px h-16px" aria-hidden="true">
                                                <use xlink:href="#svg-5-message"></use>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--<div data-v-f09a6d59="" data-v-a652ce47="" class="navigation">-->
                    <!--    <div data-v-f09a6d59="" class="navigation-content">-->
                    <!--        <div data-v-f09a6d59="" class="h-full flex cursor-pointer items-center justify-between">-->
                    <!--            <div data-v-f09a6d59="" class="icon i-material-symbols-arrow-back-ios-new-rounded"></div>-->
                    <!--            <div data-v-f09a6d59=""></div>-->
                    <!--            <div data-v-f09a6d59="" class="opacity-0">h</div>-->
                    <!--        </div>-->
                    <!--    </div>-->
                    <!--</div>-->
                                        
                        <style>
        .pt-45px {
            padding-top: 0 !IMPORTANT;
        }
    </style>
   
   <div data-v-56b6ece7="" data-v-a652ce47="" class="pt-70px home-wrap">
    
    <div data-v-56b6ece7="" class="mt-16px mb-16px">
        <div data-v-56b6ece7="" class="van-row h-35px rounded-full tab-bar justify-center">
            <div data-v-56b6ece7="" class="van-col van-col--8 text-center">Deposit History</div>
        </div>
        
        
        
        
        
           @forelse($deposits as $deposit)
        <div data-v-56b6ece7="">
            
            
            
            
                        <div data-v-56b6ece7="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text mt-16px!">
                <div data-v-56b6ece7="" class="van-row">
                    <div data-v-56b6ece7="" class="van-col van-col--8 text-center flex items-center h-37px w-full">
                        <div data-v-56b6ece7="" class="flex items-center h-full w-full">
                            <div data-v-56b6ece7=""><span data-v-56b6ece7="" class="ml-1 text-xs text-$text-gray" style="text-transform: capitalize;">{{ __($deposit->gateway?->name) }}</span></div>
                        </div>
                    </div>
                    <div data-v-56b6ece7="" class="van-col van-col--8 text-right leading-37px text-sm font-bold text-#00FF57">{{ showDateTime($deposit->created_at, 'M d Y @g:i:a') }}</div>
                    <div data-v-56b6ece7="" class="van-col van-col--8 flex justify-center text-center">
                        <div data-v-56b6ece7="" class="mx-auto h-full w-[70%] flex justify-center bg-#fff bg-opacity-12 rounded-full text-#00FF57 py-2">
                                                        <span style="color:#f9b44b;">{{ $general->cur_text }}{{ showAmount($deposit->amount) }} <br> @if ($deposit->status == 1) Successful @elseif($deposit->status == 2) Pending @elseif($deposit->status == 3) Failed @endif</span>
                                                    </div>
                                                    
                                                    
                                    
                         
                                     

                                                
                                                    
                                                    
                                                    
                                                    
                    </div>
                </div>
            </div>
            
            
            
            
            
            
            
            
            
            
            
            
            

            @empty
            Not Found
             @endforelse
            
            
            
            
            
            
            
            
            
            
            
            
                    </div>
    </div>
    
</div>
@endsection
<img src="/profelar/bl.png" style="border: none; width: 100%;" alt="PROFELAR">