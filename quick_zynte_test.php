<?php
/**
 * Quick Zynte Test - Minimal test to identify the exact issue
 */

// Zynte API Configuration
$merchant_id = 'MAM4FNXP5V';
$secret_key = 'cQuEwtz62XxHISzCRwxvBmetStqHKRWL';
$api_url = 'https://zynte.in/api/v1/order';

echo "=== Quick Zynte Test ===\n\n";

// Test data with all required parameters explicitly set
$data = [
    'customer_mobile' => '9876543210',
    'merch_id' => $merchant_id,
    'amount' => 100,
    'order_id' => 'QUICKTEST_' . time(),
    'currency' => 'INR',
    'redirect_url' => 'https://example.com/success',
    'udf1' => 'TestUser',
    'udf2' => '<EMAIL>',
    'udf3' => 'TestPayment',
    'udf4' => 'TestPlatform',
    'udf5' => 'TEST123',
];

echo "Test Data:\n";
foreach ($data as $key => $value) {
    echo "$key: $value\n";
}

// Generate X-VERIFY
function generateXVerify($data, $secret_key) {
    ksort($data);
    $dataString = implode('|', array_map(function ($key, $value) {
        return $key . '=' . $value;
    }, array_keys($data), $data));
    
    echo "\nData String for HMAC: $dataString\n";
    
    return hash_hmac('sha256', $dataString, $secret_key);
}

$xverify = generateXVerify($data, $secret_key);
echo "X-VERIFY: $xverify\n";

// Make API call
$headers = [
    'Content-Type: application/json',
    'X-VERIFY: ' . $xverify,
];

$ch = curl_init($api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Disable for testing
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);     // Disable for testing
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_VERBOSE, true);

echo "\nSending request to: $api_url\n";
echo "Headers: " . implode(', ', $headers) . "\n";
echo "Payload: " . json_encode($data) . "\n\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    echo "❌ cURL Error: " . curl_error($ch) . "\n";
} else {
    echo "✅ HTTP Code: $httpCode\n";
    echo "Response: $response\n\n";
    
    $responseData = json_decode($response, true);
    
    if ($responseData) {
        if (isset($responseData['status'])) {
            if ($responseData['status'] === true) {
                echo "✅ SUCCESS: Payment order created successfully!\n";
                echo "Order ID: " . ($responseData['result']['orderId'] ?? 'N/A') . "\n";
                echo "Payment URL: " . ($responseData['result']['payment_url'] ?? 'N/A') . "\n";
            } else {
                echo "❌ FAILED: " . ($responseData['message'] ?? 'Unknown error') . "\n";
                
                // Check for specific parameter errors
                if (strpos($responseData['message'], 'udf2') !== false) {
                    echo "🔍 UDF2 Issue Detected!\n";
                    echo "   - Check if udf2 parameter is being sent correctly\n";
                    echo "   - Verify udf2 value is not empty\n";
                    echo "   - Current udf2 value: '" . $data['udf2'] . "'\n";
                }
            }
        } else {
            echo "❌ Invalid response format\n";
        }
    } else {
        echo "❌ Failed to parse JSON response\n";
    }
}

curl_close($ch);

echo "\n=== Troubleshooting Tips ===\n";
echo "1. If you see 'missing required parameter udf2', check:\n";
echo "   - User email is not empty in your application\n";
echo "   - ProcessController is setting udf2 correctly\n";
echo "   - No encoding issues with the email value\n";
echo "\n2. If this test works but your app doesn't:\n";
echo "   - Compare the data being sent from your app vs this test\n";
echo "   - Check Laravel logs for detailed error messages\n";
echo "   - Verify user data in your database\n";
echo "\n3. Run debug_zynte_user_data.php to check your user data\n";

?>
