# Zynte Payment Gateway Integration Guide

## Overview

This guide explains how to integrate and configure the Zynte payment gateway in your 7up Cola Investment Script. Zynte is an Indian payment processor that supports multiple currencies and provides secure payment processing.

## Files Created

### 1. Backend Controller
- **File**: `core/app/Http/Controllers/Gateway/Zynte/ProcessController.php`
- **Purpose**: Handles payment processing and webhook notifications
- **Features**:
  - Payment order creation
  - Webhook verification
  - Payment status checking
  - Secure HMAC-SHA256 signature verification

### 2. Routes
- **File**: `core/routes/ipn.php` (modified)
- **Added**: `Route::post('zynte', '<PERSON><PERSON><PERSON>\ProcessController@ipn')->name('<PERSON><PERSON><PERSON>');`
- **Purpose**: Handles webhook notifications from Zynte

### 3. Frontend Views
- **Files**: 
  - `core/resources/views/templates/invester/user/payment/Zynte.blade.php`
  - `core/resources/views/templates/bit_gold/user/payment/Zynte.blade.php`
  - `core/resources/views/templates/neo_dark/user/payment/Zynte.blade.php`
- **Purpose**: User interface for Zynte payments

### 4. Database Setup
- **File**: `zynte_gateway_setup.sql`
- **Purpose**: Adds Zynte gateway configuration to database

### 5. Testing
- **File**: `test_zynte_integration.php`
- **Purpose**: Test script to verify API connectivity and credentials

## Installation Steps

### Step 1: Database Setup
1. Run the SQL script to add Zynte gateway:
```bash
mysql -u your_username -p your_database < zynte_gateway_setup.sql
```

### Step 2: Test Integration
1. Update credentials in `test_zynte_integration.php`
2. Run the test script:
```bash
php test_zynte_integration.php
```

### Step 3: Configure in Admin Panel
1. Login to admin panel
2. Go to **Payment Gateways** section
3. Find **Zynte** gateway
4. Configure the following parameters:
   - **Merchant ID**: Your Zynte merchant ID
   - **Secret Key**: Your Zynte secret key
   - **Status**: Enable the gateway

### Step 4: Configure Gateway Currencies
1. Set up supported currencies (INR, USD, etc.)
2. Configure exchange rates
3. Set minimum and maximum amounts
4. Configure fees (percentage and fixed)

## Configuration Parameters

### Gateway Parameters
```json
{
  "merchant_id": {
    "title": "Merchant ID",
    "global": true,
    "value": "YOUR_MERCHANT_ID"
  },
  "secret_key": {
    "title": "Secret Key",
    "global": true,
    "value": "YOUR_SECRET_KEY"
  }
}
```

### Supported Currencies
- INR (Indian Rupee)
- USD (US Dollar)
- EUR (Euro)
- GBP (British Pound)
- AUD (Australian Dollar)
- CAD (Canadian Dollar)
- SGD (Singapore Dollar)
- JPY (Japanese Yen)
- CNY (Chinese Yuan)
- HKD (Hong Kong Dollar)

## How It Works

### Payment Flow
1. **User initiates payment** → Selects Zynte gateway
2. **Order creation** → System calls Zynte API to create payment order
3. **Redirect to Zynte** → User redirected to Zynte payment page
4. **Payment processing** → User completes payment on Zynte
5. **Webhook notification** → Zynte sends payment status to your webhook
6. **Verification** → System verifies payment and updates user balance

### Security Features
- **HMAC-SHA256 Signature**: All API calls are signed with secret key
- **Webhook Verification**: Payment notifications are verified before processing
- **Amount Verification**: Payment amounts are cross-checked
- **Status Verification**: Payment status is verified with Zynte API

## API Endpoints Used

### 1. Create Order
- **URL**: `https://zynte.in/api/v1/order`
- **Method**: POST
- **Purpose**: Create payment order

### 2. Check Status
- **URL**: `https://zynte.in/api/v1/status`
- **Method**: POST
- **Purpose**: Verify payment status

### 3. Webhook
- **URL**: `your-domain.com/ipn/zynte`
- **Method**: POST
- **Purpose**: Receive payment notifications

## Testing

### Test Credentials (from documentation)
- **Merchant ID**: `MAM4FNXP5V`
- **Secret Key**: `cQuEwtz62XxHISzCRwxvBmetStqHKRWL`

### Test Process
1. Use test credentials for initial testing
2. Create small test payments
3. Verify webhook notifications
4. Check payment status updates
5. Validate user balance updates

## Troubleshooting

### Common Issues

#### 1. Payment Creation Fails
- **Check**: Merchant ID and Secret Key
- **Verify**: API connectivity
- **Solution**: Use test script to verify credentials

#### 2. Webhook Not Received
- **Check**: Webhook URL configuration
- **Verify**: Server can receive POST requests
- **Solution**: Test webhook endpoint manually

#### 3. Payment Verification Fails
- **Check**: HMAC signature generation
- **Verify**: Secret key matches
- **Solution**: Compare signature generation with Zynte documentation

#### 4. Amount Mismatch
- **Check**: Currency conversion rates
- **Verify**: Decimal precision
- **Solution**: Ensure amounts match exactly

### Debug Mode
Enable debug mode in ProcessController for detailed logging:
```php
// Add this to ProcessController for debugging
error_log('Zynte API Request: ' . json_encode($data));
error_log('Zynte API Response: ' . $response);
```

## Security Considerations

1. **Keep Secret Key Secure**: Never expose secret key in frontend
2. **Validate All Webhooks**: Always verify webhook signatures
3. **Use HTTPS**: Ensure all communications use SSL/TLS
4. **Log Transactions**: Maintain audit logs for all payments
5. **Regular Updates**: Keep integration updated with Zynte API changes

## Support

### Zynte Support
- **Website**: https://zynte.in
- **Documentation**: Refer to zynte.md file
- **API Support**: Contact Zynte technical team

### Integration Support
- **Files**: All integration files are well-documented
- **Testing**: Use provided test script for verification
- **Logs**: Check Laravel logs for detailed error information

## Conclusion

The Zynte payment gateway integration is now complete and ready for use. Follow the installation steps, test thoroughly, and configure according to your requirements. The integration follows Laravel best practices and maintains consistency with your existing payment gateway structure.
