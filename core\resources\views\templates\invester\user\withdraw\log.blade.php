     @extends($activeTemplate . 'layouts.master')
@section('content')
     
        <div id="app" data-v-app="" class="a-t-5 no-1">

                    <!--<div data-v-f09a6d59="" data-v-a652ce47="" class="navigation">-->
                    <!--    <div data-v-f09a6d59="" class="navigation-content">-->
                    <!--        <div data-v-f09a6d59="" class="h-full flex cursor-pointer items-center justify-between">-->
                    <!--            <div data-v-f09a6d59="" class="icon i-material-symbols-arrow-back-ios-new-rounded"></div>-->
                    <!--            <div data-v-f09a6d59=""></div>-->
                    <!--            <div data-v-f09a6d59="" class="opacity-0">h</div>-->
                    <!--        </div>-->
                    <!--    </div>-->
                    <!--</div>-->
                                        
                        <style>
        .pt-45px {
            padding-top: 0 !IMPORTANT;
        }
    </style>
   
   <div data-v-56b6ece7="" data-v-a652ce47="" class="pt-70px home-wrap">
    
    <div data-v-56b6ece7="" class="mt-16px mb-16px">
        <div data-v-56b6ece7="" class="van-row h-35px rounded-full tab-bar justify-center">
            <div data-v-56b6ece7="" class="van-col van-col--8 text-center">withdraw History</div>
        </div>
        
        
        
        
        
          @forelse($withdraws as $withdraw)
        <div data-v-56b6ece7="">
            
            
            
            
                        <div data-v-56b6ece7="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text mt-16px!">
                <div data-v-56b6ece7="" class="van-row">
                    <div data-v-56b6ece7="" class="van-col van-col--8 text-center flex items-center h-37px w-full">
                        <div data-v-56b6ece7="" class="flex items-center h-full w-full">
                            <div data-v-56b6ece7=""><span data-v-56b6ece7="" class="ml-1 text-xs text-$text-gray" style="text-transform: capitalize;">{{ __($withdraw->method?->name) }}</span></div>
                        </div>
                    </div>
                    <div data-v-56b6ece7="" class="van-col van-col--8 text-right leading-37px text-sm font-bold text-#00FF57">{{ showDateTime($withdraw->created_at, 'M d Y @g:i:a') }}</div>
                    <div data-v-56b6ece7="" class="van-col van-col--8 flex justify-center text-center">
                        <div data-v-56b6ece7="" class="mx-auto h-full w-[70%] flex justify-center bg-#fff bg-opacity-12 rounded-full text-#00FF57 py-2">
                                                        <span style="color:#f9b44b;">{{ showAmount($withdraw->amount) }} {{ $general->cur_text }} <br> @if ($withdraw->status == 1) Approved @elseif($withdraw->status == 2) Pending @elseif($withdraw->status == 3) Rejected @endif</span>
                                                    </div>
                                                    
                                                    
                                    
                         
                                     

                                                
                                                    
                                                    
                                                    
                                                    
                    </div>
                </div>
            </div>
            
            
            
            
            
            
            
            
            
            
            
            
            

            @empty
            Not Found
             @endforelse
            
            
            
            
            
            
            
            
            
            
            
            
                    </div>
    </div>
    
</div>
@endsection
