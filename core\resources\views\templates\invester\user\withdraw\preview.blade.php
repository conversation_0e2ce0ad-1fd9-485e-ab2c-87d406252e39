@extends($activeTemplate . 'layouts.master')
@section('content')
 
  <div id="app" data-v-app="" class="a-t-5 no-1">

                    <!--<div data-v-f09a6d59="" data-v-a652ce47="" class="navigation">-->
                    <!--    <div data-v-f09a6d59="" class="navigation-content">-->
                    <!--        <div data-v-f09a6d59="" class="h-full flex cursor-pointer items-center justify-between">-->
                    <!--            <div data-v-f09a6d59="" class="icon i-material-symbols-arrow-back-ios-new-rounded"></div>-->
                    <!--            <div data-v-f09a6d59=""></div>-->
                    <!--            <div data-v-f09a6d59="" class="opacity-0">h</div>-->
                    <!--        </div>-->
                    <!--    </div>-->
                    <!--</div>-->
                                        
                        <style>
        select option{
            background:#fff;
            color:#000;
        }
    </style>
    <div data-v-7aa53151="" class="withdraw-wrap p-$mg">
    <div data-v-7aa53151="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text">
        <div data-v-7aa53151="" class=":uno: flex items-center">
            <div data-v-7aa53151="" class="mr-3">
                <div data-v-7aa53151="" class=":uno: base-logo flex items-center small-logo">
                    <img class="site-img h-full w-full rd-50%" src="{{ asset(getImage(getFilePath('logoIcon') . '/logo_2.png')) }}" draggable="false" alt="logo" />
                    <!---->
                </div>
            </div>
            <div data-v-7aa53151="" class="shrink-0">
                <div data-v-7aa53151="" class=":uno: text-left text-13px lh-20px c-red">24-hour withdrawal</div>
            </div>
            <a data-v-7aa53151="" href="" class="ml-auto shrink-0 text-xl">
                <div data-v-7aa53151="" class="i-bx:bxs-food-menu text-$primary"></div>
            </a>
        </div>
        <div data-v-7aa53151="" class="number">
            <div data-v-7aa53151="" class="title">Withdrawable</div>
            <div data-v-7aa53151="" class="num">{{ showAmount(auth()->user()->interest_wallet) }}<span data-v-7aa53151="">{{ $general->cur_text }}</span></div>
        </div>
        <div data-v-7aa53151="" class="pay-type">
       
            <div data-v-7aa53151="" class="van-radio-group" role="radiogroup" style="margin-left: 10px;">
                <div data-v-7aa53151="" role="radio" class="van-radio" tabindex="0" aria-checked="true">
                    
                </div>
            </div>
        </div>
        
  
   <form action="{{route('user.withdraw.submit')}}" method="post" enctype="multipart/form-data">
        @csrf                        
         
            
  
            
            <div data-v-7aa53151="" class="base-input is-number">
            
                 
                     <x-viser-form identifier="id"  identifierValue="{{ $withdraw->method->form_id }}" />
                    
                 
                </div>
            </div>
                <div class="input-box">
          
            <button type="submit" data-v-7aa53151="" class=":uno: base-main-btn flex items-center justify-center">
                <div class="base-main-btn-content">
                    <!---->
                    <span data-v-7aa53151="">Confirm</span>
                </div>
            </button>
        </form>
    </div>
</div>

@endsection
    
  
  
  
  

       
      