<?php
/**
 * Debug script to check user data for Zynte integration
 * 
 * This script helps identify issues with user data that might cause
 * the "missing required parameter udf2" error
 */

// Include Laravel bootstrap (adjust path as needed)
require_once 'core/vendor/autoload.php';

// You might need to adjust this path based on your Laravel setup
$app = require_once 'core/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Deposit;

echo "=== Zynte User Data Debug Script ===\n\n";

// Get a sample user (you can modify this to get a specific user)
$user = User::first();

if (!$user) {
    echo "❌ No users found in database\n";
    exit;
}

echo "User Information:\n";
echo "----------------\n";
echo "ID: " . $user->id . "\n";
echo "Username: " . ($user->username ?? 'NULL') . "\n";
echo "Email: " . ($user->email ?? 'NULL') . "\n";
echo "Mobile: " . ($user->mobile ?? 'NULL') . "\n";

// Check if email is empty or null
if (empty($user->email)) {
    echo "❌ User email is empty or null - this will cause udf2 parameter to be missing\n";
    echo "Solution: Ensure user has a valid email address\n";
} else {
    echo "✅ User email is valid: " . $user->email . "\n";
}

// Check if username is empty or null
if (empty($user->username)) {
    echo "❌ User username is empty or null - this will cause udf1 parameter to be missing\n";
    echo "Solution: Ensure user has a valid username\n";
} else {
    echo "✅ User username is valid: " . $user->username . "\n";
}

// Simulate the data that would be sent to Zynte
echo "\nSimulated Zynte API Data:\n";
echo "------------------------\n";

$general = gs(); // Get general settings

$simulatedData = [
    'customer_mobile' => $user->mobile ?? '9999999999',
    'merch_id' => 'MAM4FNXP5V', // Test merchant ID
    'amount' => 100,
    'order_id' => 'TEST_' . time(),
    'currency' => 'INR',
    'redirect_url' => 'https://example.com/success',
    'udf1' => $user->username ?? 'User',
    'udf2' => $user->email ?? '<EMAIL>',
    'udf3' => 'Investment Deposit',
    'udf4' => $general->site_name ?? 'Investment Platform',
    'udf5' => '123',
];

foreach ($simulatedData as $key => $value) {
    if (empty($value)) {
        echo "❌ $key: EMPTY/NULL\n";
    } else {
        echo "✅ $key: $value\n";
    }
}

// Check for potential issues
echo "\nPotential Issues Check:\n";
echo "----------------------\n";

$issues = [];

if (empty($simulatedData['udf1'])) {
    $issues[] = "udf1 (username) is empty";
}

if (empty($simulatedData['udf2'])) {
    $issues[] = "udf2 (email) is empty";
}

if (empty($simulatedData['udf3'])) {
    $issues[] = "udf3 is empty";
}

if (empty($simulatedData['udf4'])) {
    $issues[] = "udf4 (site name) is empty";
}

if (empty($simulatedData['udf5'])) {
    $issues[] = "udf5 is empty";
}

if (empty($issues)) {
    echo "✅ No issues found with user data\n";
} else {
    echo "❌ Issues found:\n";
    foreach ($issues as $issue) {
        echo "   - $issue\n";
    }
}

// Test X-VERIFY generation
echo "\nTesting X-VERIFY Generation:\n";
echo "---------------------------\n";

function generateXVerify($data, $secret_key) {
    ksort($data);
    $dataString = implode('|', array_map(function ($key, $value) {
        return $key . '=' . $value;
    }, array_keys($data), $data));
    
    echo "Data string: $dataString\n";
    
    return hash_hmac('sha256', $dataString, $secret_key);
}

$secret_key = 'cQuEwtz62XxHISzCRwxvBmetStqHKRWL';
$xverify = generateXVerify($simulatedData, $secret_key);
echo "Generated X-VERIFY: $xverify\n";

echo "\nRecommendations:\n";
echo "---------------\n";
echo "1. Ensure all users have valid email addresses\n";
echo "2. Ensure all users have valid usernames\n";
echo "3. Check Laravel logs for detailed error messages\n";
echo "4. Test with the updated ProcessController that has fallback values\n";
echo "5. Use the test script to verify API connectivity\n";

?>
