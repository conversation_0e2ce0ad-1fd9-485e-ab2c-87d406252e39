@extends($activeTemplate . 'layouts.master')
@section('content')


          <div id="app" data-v-app="" class="a-t-5 no-1">
   
                    <!--<div data-v-f09a6d59="" data-v-a652ce47="" class="navigation">-->
                    <!--    <div data-v-f09a6d59="" class="navigation-content">-->
                    <!--        <div data-v-f09a6d59="" class="h-full flex cursor-pointer items-center justify-between">-->
                    <!--            <div data-v-f09a6d59="" class="icon i-material-symbols-arrow-back-ios-new-rounded"></div>-->
                    <!--            <div data-v-f09a6d59=""></div>-->
                    <!--            <div data-v-f09a6d59="" class="opacity-0">h</div>-->
                    <!--        </div>-->
                    <!--    </div>-->
                    <!--</div>-->
                                        
                        <style>
        .pt-45px {
            padding-top: 0 !IMPORTANT;
        }
    </style>
   
   <div data-v-56b6ece7="" data-v-a652ce47="" class="pt-70px home-wrap">
    
    <div data-v-56b6ece7="" class="mt-16px mb-16px">
        <div data-v-56b6ece7="" class="van-row h-35px rounded-full tab-bar justify-center">
            <div data-v-56b6ece7="" class="van-col van-col--8 text-center">Transactions</div>
        </div>
          @forelse($transactions as $transaction)
        
        <div data-v-56b6ece7="">
            
                        <div data-v-56b6ece7="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text mt-16px!">
                <div data-v-56b6ece7="" class="van-row">
                    <div data-v-56b6ece7="" class="van-col van-col--8 text-center flex items-center h-37px w-full">
                        <div data-v-56b6ece7="" class="flex items-center h-full w-full">
                            <div data-v-56b6ece7=""><span data-v-56b6ece7="" class="ml-1 text-xs text-$text-gray" style="text-transform: capitalize;">{{ __(keyToTitle($transaction->remark)) }}</span></div>
                        </div>
                    </div>
                    <div data-v-56b6ece7="" class="van-col van-col--8 text-right leading-37px text-sm font-bold text-#00FF57">{{ showDateTime($transaction->created_at, 'M d Y @g:i:a') }}</div>
                    <div data-v-56b6ece7="" class="van-col van-col--8 flex justify-center text-center">
                        <div data-v-56b6ece7="" class="mx-auto h-full w-[70%] flex justify-center bg-#fff bg-opacity-12 rounded-full text-#00FF57 py-2">
                          {{ showAmount($transaction->amount) }} {{ $general->cur_text }}
                        </div>
                    </div>
                </div>
            </div>
            
                
      @empty
      
      <div data-v-cbbf7adf="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text scale-x"><div data-v-cbbf7adf="" class="task-list"><!----><div data-v-e7ebc72f="" class="base-list a-t-2"><div data-v-e7ebc72f="" class="base-list-nodata">No data</div><!----><!----><!----></div></div></div>
      
      @endforelse
            
            
                    </div>
    </div>
    
</div>

  
      
     @endsection

     <img src="/profelar/bl.png" style="border: none; width: 100%;" alt="PROFELAR">