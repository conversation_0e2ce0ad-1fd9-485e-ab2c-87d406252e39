PayIN API Documentation
This documentation provides details on how to use the API to initiate payment requests.

Endpoint PayIN Api
URL: https://zynte.in/api/v1/order

Method: POST

Request Headers
Parameter	Type	Description
X-VERIFY	string	To Verify Data Integrity
Formula for Generating X-VERIFY
The X-VERIFY header is generated using the following formula:

X-VERIFY = hash_hmac('sha256', dataString, secret_key)
Where:

dataString is a string formed by concatenating parameter names and values in the format parameter1=value1|parameter2=value2|..., sorted by parameter names.
secret_key is the user's secret key used for generating the HMAC.
Request Parameters
Parameter	Type	Description
customer_mobile	int	Number of payee.
merch_id	string	Merchant ID.
amount	int	The Pay amount.
order_id	string	Unique order ID.
currency	string	Currency code.
redirect_url	string	URL to redirect.
udf1	string	UDF field 1.
udf2	string	UDF field 2.
udf3	string	UDF field 3.
udf4	string	UDF field 4.
udf5	string	UDF field 5.
Response
Parameter	Type	Description
status	boolean	API Request status.
result	object	Contains result if true .
message	string	Message for failure.
Example - PayIN Request:
<?php

// API endpoint URL
$url = 'https://zynte.in/api/v1/order';

// Data to be sent in the POST request
$data = [
  'customer_mobile' => '9876543210',
  'merch_id' => 'MAM4FNXP5V',
  'amount' => 100,
  'order_id' => 'ORD' . time(),
  'currency' => 'INR',
  'redirect_url' => 'https://example.com/thankyou',
  'udf1' => 'CustomData1',
  'udf2' => 'CustomData2',
  'udf3' => 'CustomData3',
  'udf4' => 'CustomData4',
  'udf5' => 'CustomData5',
];

$datatosend = json_encode($data);

// Generate xverify
function generatexverify($data, $secret_key) {
    ksort($data);
    $dataString = implode('|', array_map(function ($key, $value) {
        return $key . '=' . $value;
    }, array_keys($data), $data));
    return hash_hmac('sha256', $dataString, $secret_key);
}

// Generate xverify
$secret_key = 'cQuEwtz62XxHISzCRwxvBmetStqHKRWL';
$xverify = generatexverify($data, $secret_key);

// Headers for the request
$headers = [
  'Content-Type: application/json',
  'X-VERIFY: ' . $xverify,
];

// Initialize cURL session
$ch = curl_init($url);

// Set cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $datatosend);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

// Execute the cURL session
$response = curl_exec($ch);

// Check for cURL errors
if (curl_errno($ch)) {
  echo curl_error($ch);
} else {
  echo $response;
}

// Close cURL session
curl_close($ch);
?>
Successful Response
{
  "status": true,
  "message": "Order Created Successfully",
  "result": {
    "orderId": "ORD1728403472",
    "currency": "INR",
    "payment_url": "https://zynte.in/pay/fast-pay/28a1677dd13f07"
  }
}
Failed Response
{
"status": false,
"message": "Error message describing the issue"
}
Note: Always check for the status field in the response to determine the success or failure of the API call. The message field provides additional information about the response.
Order Status API
This API allows you to check the status of an order by sending a Api request with specific parameters.

Endpoint for Order Status
URL: https://zynte.in/api/v1/status

Method: POST

Request Headers
Parameter	Type	Description
X-VERIFY	string	To Verify Data Integrity
Formula for Generating X-VERIFY
The X-VERIFY header is generated using the following formula:

X-VERIFY = hash_hmac('sha256', dataString, secret_key)
Where:

dataString is a string formed by concatenating parameter names and values in the format parameter1=value1|parameter2=value2|..., sorted by parameter names.
secret_key is the user's secret key used for generating the HMAC.
Request Parameters
Parameter	Type	Description
order_id	string	Unique order identifier.
merch_id	string	Merchant ID.
Example - Check PayIN Status
<?php

// API endpoint URL
$url = 'https://zynte.in/api/v1/status';

// Data to be sent in the POST request
$data = [
  'order_id' => 'ORD1728403472',
  'merch_id =>'MAM4FNXP5V',
];

// Function to generate xverify
function generatexverify($data, $secret_key) {
    // Sort the data by keys to ensure consistent order
    ksort($data);
    $dataString = implode('|', array_map(function ($key, $value) {
        return $key . '=' . $value;
    }, array_keys($data), $data));
    return hash_hmac('sha256', $dataString, $secret_key);
}

// Generate xverify
$secret_key = 'cQuEwtz62XxHISzCRwxvBmetStqHKRWL';
$xverify = generatexverify($data, $secret_key);

// Headers for the request
$headers = [
  'Content-Type: application/json',
  'X-VERIFY: ' . $xverify,
];

// Initialize cURL session
$ch = curl_init($url);

// Set cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

// Execute the cURL session
$response = curl_exec($ch);

// Check for cURL errors
if (curl_errno($ch)) {
  echo curl_error($ch);
} else {
  echo $response;
}

// Close cURL session
curl_close($ch);
?>
Successful Response
{
  "status": true,
  "message": "Fetched Successfully",
  "result": {
    "order_id": "TY2781",
    "txnStatus" : "SUCCESS", //PENDING,
    "currency": "INR", //Can be other
    "time": 1728567194
  }
}
Failed Response
{
"status": false,
"message": "Error message describing the issue"
}
Webhook Response
When a payment is completed, the payment gateway will send a webhook notification to your specified URL Using Post Method.

Webhook Example PHP
<?php
$status = $_POST['status'];
$order_id = $_POST['order_id'];
$customer_mobile = $_POST['customer_mobile'];
$amount = $_POST['amount'];
$currency_code = $_POST['currency_code'];
$udf1 = $_POST['udf1'];
$udf2 = $_POST['udf2'];
$udf3 = $_POST['udf3'];
$udf4 = $_POST['udf4'];
$udf5 = $_POST['udf5'];
$time = $_POST['time'];
?>
Note: Ensure you validate the webhook data to confirm the authenticity of the payment notification.