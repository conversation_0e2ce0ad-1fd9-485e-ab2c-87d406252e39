<?php

// This class was generated on Wed, 01 Aug 2018 16:35:04 PDT by version 0.1.0-dev+0ee05a-dirty of Braintree SDK Generator
// CapturesRefundRequest.php
// @version 0.1.0-dev+0ee05a-dirty
// @type request
// @data 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
// DO NOT EDIT

namespace App\Http\Controllers\Gateway\PaypalSdk\Payments;

use App\Http\Controllers\Gateway\PaypalSdk\PayPalHttp\HttpRequest;

class CapturesRefundRequest extends HttpRequest
{
    function __construct($captureId)
    {
        parent::__construct("/v2/payments/captures/{capture_id}/refund?", "POST");

        $this->path = str_replace("{capture_id}", urlencode($captureId), $this->path);
        $this->headers["Content-Type"] = "application/json";
    }


    public function payPalRequestId($payPalRequestId)
    {
        $this->headers["PayPal-Request-Id"] = $payPalRequestId;
    }
    public function prefer($prefer)
    {
        $this->headers["Prefer"] = $prefer;
    }
}
