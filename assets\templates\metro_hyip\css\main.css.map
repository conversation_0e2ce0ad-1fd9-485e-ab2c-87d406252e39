{"version": 3, "sources": ["abstracts/_variable.scss", "abstracts/_mixin.scss", "abstracts/_typography.scss", "abstracts/_classess.scss", "abstracts/_extend.scss", "abstracts/_global.scss", "base/_bg-color.scss", "base/_color.scss", "base/_margin.scss", "base/_padding.scss", "base/_border.scss", "components/_accordion.scss", "components/_button.scss", "components/_card.scss", "components/_form.scss", "components/_modal.scss", "components/_pagination.scss", "components/_table.scss", "components/_tab.scss", "components/_badge.scss", "components/_floating-input.scss", "layout/_header.scss", "layout/_breadcumb.scss", "layout/_footer.scss", "layout/_preloader.scss", "layout/_scroll-top.scss", "layout/_selection.scss", "layout/_social-icon.scss", "layout/_blog-sidebar.scss", "layout/_dashboard-body-fluid.scss", "layout/_body-overlay.scss", "layout/_search-form.scss", "layout/_sidebar-menu-fluid.scss", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/home/<USER>", "partials/_blog-details.scss", "partials/_contact.scss", "partials/_account.scss"], "names": [], "mappings": "AAAQ;AAER;AACE;EAEE;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;AAEH;EAEI;EAGD;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;AAEA;EAEA;EAEA;EAEA;AACA;EAEA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEC;EACA;EACA;EACA;EACA;AACE;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAOF;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAED;AACC;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AAED;EACC;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AAED;EACC;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;;;AAEJ;AC3NA;AAEA;AAgEA;AC5IA;AACA;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EAEA;EACA;EACA;;;AAEJ;EACE;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;EACA;EACA;EACA;EACA;;;ADDF;ECTA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYI;;;AAGJ;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;;;AAGJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEF;EACE;;;AAEF;AAAA;EAEE;EACA;EACA;;;AAEF;EACE;;;AAEF;EACE;EACA;EACA;EACA;;;AACA;EACE;;;AAGJ;EACE;EACA;;;AAEJ;AC7HA;AACA;AAEI;EADJ;IAEQ;;;AAIR;EACI;EACA;EACA;;;AAIJ;AACA;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;AACA;EACI;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGR;EACI;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;AACA;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;;;AAEH;EACG;;;AFvCF;EE2CF;IAEQ;IACA;IACA;IACA;IACA;;;AAGR;AChGA;AACA;EACI;EACA;EACA;;;AAEJ;AACE;EACE;EACA;;;AAEF;EACE;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AAEJ;AAEA;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AHpBF;EGmBF;IH6CE;;;AGvCF;EACI;;;AHZF;EGWF;IAGQ;;;AH5BN;EGyBF;IHuCE;;;AG9BF;EACI;;;AHrBF;EGoBF;IHiCE;;;AAnEA;EGkCF;IH8BE;;;AGrBF;AAGA;AACA;EACI;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAEJ;ACnGA;AACA;EACE;EACA;EACA;EACA;;;AJwBA;EI5BF;IAMI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;EACA;;;AAGF;EACE;;;AJFF;EICA;IAGI;;;AAIJ;EACE;EACA;EACA;EACA;;;AJLF;EICA;IAMI;;;AAGA;EACE;EACA;EACA;EACA;EACA;EACA;;;AJhBN;EIUI;IAQM;IACA;IACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AJ9BV;EIsBQ;IAUI;;;AAIR;EACE;EACA;;;AAKR;AAEA;AACA;EACE;;;AACA;EACI;;;AAEJ;EACI;EACA;;;AACA;EACI;;;AAKV;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AJrEA;EIqDF;IAkBI;IACA;IACA;;;AJvFF;EImEF;IAuBI;IACA;;;AAED;EACC;EACA;;;AAGJ;EACE;;;AJrFA;EIoFF;IAGI;;;AJrGF;EIkGF;IAMI;;;AAGJ;EACE;;;AJ9FA;EI6FF;IAGI;;;AJ9GF;EI2GF;IAMI;;;AAIJ;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AJhHA;EIiGF;IAiBI;;;AJhIF;EI+GF;IAoBI;IACA;IACA;;;AAGJ;EACE;EACA;;;AAEF;EACE;EACA;;;AJ9IA;EI4IF;IAII;IACA;;;AAIJ;EACE;;;AJtJA;EIqJF;IAGI;IACA;;;AAIJ;AACA;EACI;EACA;;;AACA;EACI;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAKZ;AC5NA;AAEI;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAGR;AC3BA;AAEI;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAGR;AC9BA;AACA;EACE;EACA;;;AACA;EAHF;IAIG;IACA;;;AAGH;EACE;;;AACA;EAFF;IAGG;;;AAGH;EACE;;;AACA;EAFF;IAGG;;;AAIH;EACE;EACA;;;AACA;EAHF;IAIG;IACA;;;AAGH;EACE;;;AACA;EAFF;IAGG;;;AAGH;EACE;;;AACA;EAFF;IAGG;;;AAIH;EACE;EACA;;;AACA;EAHF;IAIE;IACA;;;AAGF;EACA;;;AACA;EAFA;IAGA;;;AAGA;EACA;;;AACA;EAFA;IAGA;;;AAGA;EACE;EACA;;;AACF;EAHA;IAIA;IACA;;;AAGA;EACA;;;AACA;EAFA;IAGA;;;AAGA;EACA;;;AACA;EAFA;IAGA;;;AAGA;ACnFA;AACA;EACE;EACA;;;AACA;EAHF;IAII;IACA;;;AAEF;EAPF;IAQI;IACA;;;AAGJ;EACI;;;AACA;EAFJ;IAGI;;;AAEA;EALJ;IAMI;;;AAGJ;EACI;;;AACA;EAFJ;IAGI;;;AAEA;EALJ;IAMI;;;AAGJ;EACI;EACA;;;AACA;EAHJ;IAII;IACA;;;AAEA;EAPJ;IAQI;IACA;;;AAGJ;EACI;;;AACA;EAFJ;IAGI;;;AAEA;EALJ;IAMI;;;AAGF;EACE;;;AACA;EAFF;IAGE;;;AAEA;EALF;IAME;;;AAKJ;EACK;;;AACA;EAFL;IAGO;;;AAEF;EALL;IAMO;;;AAGL;EACE;;;AACA;EAFF;IAGI;;;AAEF;EALF;IAMI;;;AC7EN;AAEI;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAGR;AC5BA;AAEI;EACI;EACA;EACA;EACA;;;AACA;EACI;;;AAGR;EACI;EACA;;;AACA;EACI;EACA;;;AVcV;EUhBM;IAKQ;;;AAIZ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;EAEA;EACA;EACA;EACA;;;AVPN;EUDE;IAUQ;IACA;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;EACA;;;AAIJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AVtCd;EU6BU;IAWQ;;;AAMR;EACI;EACA;;;AAMhB;ACpFA;AACA;EACI;EACA;;;AACA;EACE;EACA;;;AAIN;EAKM;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA+BF;;;AA5CA;EACI;EACA;;;AXwBN;EW3BF;IAgBU;;;AXIR;EWpBF;IAmBU;;;AAEJ;EACI;EACA;;;AAEJ;EACE;;;AXCN;EWFI;IAGM;;;AXRV;EWKI;IAMM;;;AAGN;EACE;;;AAGF;EACE;EACA;EACA;EAEA;;;AAGJ;EACI;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;;;AAMJ;EACI;;;AAEJ;EACC;EACA;EACA;EACA;EACA;;;AACA;EACG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEH;EACG;;;AAGH;EACG;;;AAIR;EACI;EACA;EACA;EACA;;;AACA;EAEI;;;AAGR;EACI;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAIZ;EACI;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAIZ;EACI;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAIZ;EACI;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAIZ;EACI;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAIZ;EACI;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAIZ;EACI;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAIZ;EACI;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAKhB;ACvRA;AACA;EACI;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAGR;EACI;EACA;EACA;;;AAGR;AC3BA;AACA;EACI;EAEA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AbPF;EaLF;IAcQ;;;AAEJ;EACI;;;AbsBN;EavBE;IAIQ;;;AbRV;EaIE;IAOQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;;;AACA;EACI;;;AAGR;EACI;EACA;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;AAKhB;EACI;EACA;;;AAGJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAIJ;EACI;;;AAEJ;EACI;EACA;;;AAEJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;AACA;EACI;EACA;;;AAEJ;EACI;;;AAEJ;AAAA;EAEI;;;AAEJ;EACI;;;AAEJ;AAGA;EACI;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;EACA;;;AAIR;AACA;EACI;EACA;;;AACA;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;;;AAIZ;EACI;EACA;EACA;EACA;;;AbjKN;EamKE;Ib7FF;;;AAtEA;EawKE;IblGF;;;AawGF;AAEI;EACI;EACA;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;EACA;;;AACA;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKhB;AAEC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACM;EACA;;;AACN;EACC;EACA;EACA;EACA;EACA;EACA;;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACS;EACT;;;AAED;EACC;;;AACS;EACI;;;AAEb;EACC;EACY;;;AAKhB;EACC;EACA;;;AACA;EACC;EACA;EACA;EACA;EACA;;;AAGF;AACA;AACA;EACI;;;AAEF;EACE;;;AAEJ;ACzSA;AAEI;EACI;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;;;AAIZ;EACI;EACA;;;AAGJ;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGR;ACnDA;AACA;EACI;EACA;EACA;;;Af+CF;EelDF;IAKQ;;;AfsCN;Ee3CF;IAQQ;;;Af4BN;EepCF;IAWQ;;;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;;;AAEJ;EACI;;;AAKhB;ACtCA;AACA;AACA;EACC;EAEA;EACA;EACA;EACA;EACA;;;AACA;EACC;;;AAGE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AhBsBH;EgB9BE;IAUE;;;AAED;EACC;;;AAED;EACC;EACA;;;AAED;EACC;EACA;;;AAED;EACC;;;AAKJ;EACC;;;AACA;EACC;EACA;EACA;EACA;EACA;;;AAEC;EACC;;;AhBTJ;EgBQG;IAGE;;;AAED;EACC;;;AAED;EACC;;;AhBjBL;EgBCC;IAqBE;;;AAED;EACC;;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AhBtBH;EgBaE;IAWE;;;AhB/BJ;EgBoBE;IAcE;;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAED;EACC;;;AAGD;EACC;;;AAOJ;EACC;;;AACA;EACC;EACA;EACA;EACA;EACA;EACA;EACY;EACZ;;;AhB5EF;EgBoEC;IAUE;;;AAED;EACC;;;AAED;EACC;EACA;;;AAED;EACC;EACA;;;AAED;EACC;;;AAKJ;EACC;;;AACA;EACC;EACA;EACA;;;AACA;EACC;;;AAED;EACC;;;AAED;EACC;EACA;EACA;EACY;EACZ;EACA;EACA;EACA;EACA;;;AhBtHF;EgB6GC;IAWE;IACA;IACA;IACA;IACA;;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACe;EACA;;;AAEhB;EACC;;;AAGD;EACC;;;AhB9IH;EgBsMC;IACC;;;EAGA;IACC;IACA;IACA;;;EACA;IACC;;;EAED;IACC;;;EAEA;IACD;;;EAGC;IACC;;;EAIF;IACC;;;EAED;IACC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EACA;IACC;;;EAED;IACC;IACA;;;EAED;IACC;IACA;;;AhBlPL;EgB2PG;IACC;;;AAMN;AhBlQE;EgBsQC;IACC;;;EAGA;IACC;IACA;IACA;;;EACA;IACC;;;EAED;IACC;;;EAEA;IACD;;;EAGC;IACC;;;EAIF;IACC;;;EAED;IACC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EACA;IACC;;;EAED;IACC;IACA;;;EAED;IACC;IACA;;;AhBlTL;EgB2TG;IACC;;;AAON;AC/WA;AACA;EACI;EACA;;;AjB2BF;EiB7BF;IAIQ;;;AjBmBN;EiBvBF;IAOQ;;;AAEJ;EACI;EACA;;;AACA;EACI;EACA;EACA;EACC;EACD;EAEA;EACA;EACA;;;AjB6BV;EiBtCM;IAYQ;;;AjBDd;EiBXM;IAeQ;;;AAEJ;EACI;EACA;;;AAEA;EACI;;;AAGR;EACI;;;AAKhB;AC5CA;AACA;EACI;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAGR;AC9CA;AA6FA;AAEC;EACC;EACA;EACA;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEA;EACC;EACA;EACA;;;AAGD;EACC;EACA;;;AAEA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;;;AAMA;EACC;EACA;EACA;EACA;EACA;EACA;;;ACrKL;AACA;EACI;EACA;;;ApBwCF;EoBvCE;IAEQ;;;AAEJ;EACI;EACA;EACA;EACA;;;ApBsCV;EoB1CM;IAMQ;;;ApB6Bd;EoBnCM;IASQ;;;ApBYd;EoBrBM;IAYQ;;;AAMhB;EACI;EACA;EACA;EACA;EACA;EACA;;;ApBWF;EoBjBF;IAQQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EACA;IACI;IACA;;;EAEJ;IACI;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACI;IACI;IACA;;EAEJ;IACI;IACA;;;AAIR;EACI;;;AAEJ;AACA;EACA;IACI;IACA;IACA;;;EAEA;IACI;IACA;;;ApBdN;EoBYE;IAIQ;;;ApBpCV;EoBgCE;IAOQ;;;AAbZ;EAiBY;IACI;;;EACA;IACI;;;EAEJ;IACI;IACA;;;AAxBpB;EA4BQ;IACI;IpBHV;IoBKU;IACA;IACA;IACA;;;EACA;IACI;;;EACA;IACI;;;EAIR;IACI;IACA;;;EAEJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;;;EAEJ;IACI;IpB/Cd;IoBiDc;;;ApB7Fd;EoB0FU;IAKQ;;;AA9DpB;EAoEA;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EACA;IACI;;;EACA;IACI;;;EAGR;IACI;IACA;IpBhEN;IoBkEM;;;EACA;IACI;IACA;;;AA/FZ;EAuGY;IACI;IACA;IACA;IACA;;;AAOhB;EACI;;;AAEJ;EACI;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;;;AACA;EAEI;;;AAEJ;EACI;EACA;;;AAKR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGR;AAEA;ApBtLE;EoBwLE;IACI;IACA;;;EAEJ;IACI;;;EACA;IACI;IACA;IACA;IACA;IACA;;;EACA;IACI;;;EAKR;IACI;;;EAEJ;IACI;;;EAEJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EACA;IACI;;;EAGA;IACI;;;EACA;IACI;;;EAMpB;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EAEM;IACE;;;EAEJ;IACI;IACA;IpBpMd;IoBsMc;IACA;IACA;IACA;;;EACA;IACI;;;AAQhB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACA;;;AAEA;EACI;;;AAEJ;EACI;;;AAKZ;EACI;;;ApB9RF;EoB6RF;IAGQ;IACA;IACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAGL;EACK;EACA;EACA;EACA;;;ApB/TN;EoB2TC;IpB3PD;;;AoBuQF;ACpWA;AACA;EACI;EACA;EACA;;;AAEA;EACI;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAGJ;EACI;EACA;EACA;;;AAEA;EACI;;;AAEJ;EACI;;;AAGR;EACI;EACA;;;AACA;EACI;;;AAGR;EACG;EACA;EACC;;;ArBGN;EqBNE;IAKQ;IACA;;;ArBPV;EqBCE;IAUQ;IACA;IACA;IACA;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ArBjCN;EqBkBE;IAiBO;;;AAIX;AC3EA;AACA;EACI;;;AACA;EACI;EACA;EACA;EACA;;;AtB6BN;EsBjCE;IAMQ;IACA;IACA;IACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AtBKN;EsBrBE;IAkBQ;;;AAeZ;EACI;EACA;EACA;;;AAEJ;EACI;IACI;;;AAIJ;EACI;;;AAEI;EACI;EACA;EACA;EACA;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AtBrCN;EsBoCE;IAGQ;;;AtBrDV;EsBkDE;IAMQ;;;AAKZ;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AACA;EACI;EACA;;;AAKZ;EACI;;;AACA;EACI;EACA;;;AACA;EACI;EACA;EACA;;;AAEJ;EACI;EACA;;;AAIZ;AAEA;AACA;EAEI;;;AACA;EACI;EACA;EACA;EACA;;;AtBjHN;EsBmHE;ItB7CF;;;AsBiDM;EACI;EACA;EACA;;;AAEA;EACI;;;AAKhB;AC1JA;AACA;EACE;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;IACE;IACA;;EAEF;IACE;;EAEF;IACE;IACA;;;AAGJ;AC/CA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;;AxBgBF;EwBnCF;IAqBQ;IACA;IACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;EACA;;;AAGR;EACI;IACI;;EAEJ;IACI;;;AAGR;AC7CA;AACA;EACI;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;EACA;;;AAGJ;AClBA;AACA;EACI;EACA;EACA;;;AACA;EACI;;;AACA;EACI;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;A1BQV;E0BzBE;IAoBQ;IACA;I1B0DV;;;A0BvDM;EACI;EACA;EACA;;;AAIZ;AC1CA;AACA;EACI;;;AACA;EACI;EACA;EACA;EACA;EACA;;;AAIR;AACA;EACI;EACA;EACA;EACA;;;AACA;EACI;;;A3BUN;E2BhBF;IASQ;;;AAEJ;EACI;EACA;EACA;;;AAIR;AAIQ;EACI;;;AAEJ;EACI;EACA;;;AACA;EACI;;;AAoChB;AC/EA;AACA;AAeI;AAkHA;AAEA;AA6KD;AA+IC;AACD;;;AA/bC;EACI;;;AACA;EACI;EACA;;;AAGJ;EACI;;;A5ByCV;E4B1CM;IAGQ;;;AAKZ;EACI;;;AAIJ;EACI;EACA;;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAGA;EACI;EACA;EACA;EACA;EACA;;;AAOZ;EACI;EACA;;;AACA;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;A5B5Bd;E4BWM;IAoBQ;IACA;;;AAII;EACI;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;EACA;;;AAIZ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAMJ;EACI;EAEA;EACA;EACA;;;AAMR;EACI;EACA;;;A5BpFN;E4BkFE;IAIQ;;;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;A5B/HN;E4BsHE;IAWQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;A5B3JV;E4BwJM;IAKQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAGR;EACI;EAEA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;;A5BzMV;E4BmMM;IAQQ;;;AAGR;EACE;EACA;EACA;EACA;;;AACA;EALF;IAMI;;;A5BlLV;E4B4KM;IASI;;;A5B3LV;E4BkLM;IAYK;;;A5B1NX;E4B8MM;IAeI;IACA;;;A5BrOV;E4BqNM;IAmBI;;;AAGJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;;;A5B1MV;E4BwMM;IAIQ;;;A5B9Od;E4B0OM;IAOQ;;;A5BxPd;E4BiPM;IAUQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;A5BpQV;E4BuPM;IAeQ;IACA;IACA;;;AAKZ;EACI;;;A5BhQN;E4B+PE;IAGQ;;;A5BhRV;E4B6QE;IAMQ;;;A5BhSV;E4B0RE;IASQ;;;AAGN;EACE;EACA;EACA;EACA;EACA;;;A5BhRN;E4B2QI;IAOM;;;AAGF;EACE;;;AACA;EACI;EACA;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAMd;EACE;EACA;EACA;;;AAGA;EACI;EACA;EACA;EACA;;;AAGN;EACE;;;AACA;EACI;EACA;EACA;EACA;;;AAKP;EACG;EACA;EACA;EACA;;;AACA;EACI;;;A5B7VV;E4B4VM;IAGQ;;;AAGR;EACI;;;AAEJ;EACI;EACA;EACA;EACA;;;A5BpYV;E4BgYM;IAMQ;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;A5BzVd;E4BwVU;IAGQ;;;AAGR;EACI;;;AAGR;EACI;;;AACA;EACI;;;AAEJ;EACI;EACA;EACA;EACA;;;AAGR;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;;;AAKR;EACI;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AAEJ;EACE;;;AAED;EACG;;;AAEH;EACG;EACA;;;AAEH;EACG;;;AAEH;EACG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;;;AAGR;EACI;EACA;;;AAMR;EACI;EACA;;;AAEJ;AC1gBA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;AAGR;ACrCA;AACA;EACI;EACA;EACA;EACA;;;A9ByBF;E8B7BF;IAMQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEA;EACI;EACA;;;AAIZ;ACvCA;AACA;AAkBC;AA8CA;AA8JC;;;AA5NA;EACE;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;;;AACA;EACE;EACA;;;AAMN;EACE;;;AACA;EACE;;;AAEF;EACE;EACA;EACA;EACA;;;AACA;EAEE;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AAEF;EAEE;;;AAGJ;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;;;AAOR;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAKA;AAoCF;;;AAxCE;EACE;EACA;;;AAGF;EACI;;;A/BlCN;E+BgBA;IAqBM;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;AAKR;EACE;;;AAEI;EACE;EACA;EACA;;;AAII;EACE;EACA;EACA;;;AACE;EACI;EACA;EACA;;;AAKR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEA;EACE;;;AAEF;EACI;EAEA;EACA;EACA;;;AAOd;EACE;;;AACA;EACE;;;AAEF;EACI;;;AAGI;EACE;EACA;;;AAIN;EACE;EACA;EACA;EACA;EACA;EAEA;;;AACE;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAOhB;ACjOA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;;AhC4CF;EgClDF;IAQQ;;;AhCmCN;EgC3CF;IAWQ;;;AhCyBN;EgCpCF;IAcQ;;;AhCeN;EgC7BF;IAiBQ;;;AhC0BN;EgCxBE;IAEQ;IACA;;;AAKZ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;;;AhCLF;EgCGF;IAIQ;;;AAEJ;EACI;EACA;;;AhCJN;EgCEE;IAIQ;;;AhCbV;EgCSE;IAOQ;;;AhCvBV;EgCgBE;IAUQ;;;AAGR;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AhCvBN;EgCmBE;IAMQ;;;AhChCV;EgC0BE;IASQ;IACA;;;AAIZ;AAEA;EACI;EACA;EACA;EACA;;;AhChCF;EgC4BF;IAMQ;;;AhCzCN;EgCmCF;IASQ;;;AhCnDN;EgC0CF;IAYQ;IACA;;;AhC9DN;EgCiDF;IAgBQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;AACA;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;IACE;;EAGF;IACE;;;AAIJ;EACE;IACE;;EAGF;IACE;;;AAGN;AC3TA;AACA;EACI;;;AASA;EACI;EACA;EACA;;;AjCgBN;EiCnBE;IAKQ;;;AjCQV;EiCbE;IAQQ;;;AAGR;EAEI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAOR;ACzCA;AACA;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AlCOF;EkCbF;IAQQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AlCnBN;EkCeE;IAMQ;IACA;;;AAMR;EACI;EACA;;;AlC9BN;EkC4BE;IAIQ;IACA;;;AAGR;EACI;EACA;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;EACA;;;AlC/CN;EkC2CE;IAMQ;IACA;;;AAIZ;AC9EA;AACA;EACI;EACA;EACA;EACA;;;AAIY;EACI;;;AAIJ;EACI;;;AAKhB;EACI;EACA;EACA;;;AACA;EACI;EACA;;;AACA;EAEI;;;AAIZ;EACI;EACA;EACA;;;AnCCN;EmCJE;IAKQ;;;AnCdV;EmCSE;IAQQ;;;AAGR;EACI;;;AACA;EACI;EACA;EACA;EACA;EACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;;AnCvBN;EmCiBE;IAQQ;;;AAIZ;EACI;EACA;EACA;;;AnChCF;EmC6BF;IAKQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AnCrDN;EmC8CE;IASQ;IACA;;;AnC/DV;EmCqDE;IAaQ;;;AAIZ;ACpGA;AACA;EACI;EACA;EACA;EACA;;;AAEJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;ApC8BA;EoCrCF;IASI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;ApCkBF;EoCzBF;IASQ;;;AAGN;EACE;IACE;;EAGF;IACE;;EAGF;IACE;;;AAGN;ApCAE;EoCCF;IAGQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;ApCdF;EoCOF;IASQ;;;ApC9BN;EoCqBF;IAYQ;;;ApCvCN;EoC2BF;IAeQ;;;AAGR;EACI;;;AAEH;EACG;;;AAGA;EAGI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGR;EACI;EACA;EACA;EACA;;;AACA;EACG;;;ApCzDL;EoCwDE;IAGI;;;AAGJ;EACI;EACA;EAEA;EACA;EACA;EACA;EACA;;;AACA;EACI;;;AAIZ;AAEA;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ApC3EF;EoCkEF;IAWQ;;;ApCpFN;EoCyEF;IAcQ;IACA;;;ApC5GN;EoC6FF;IAkBQ;;;AAEJ;EACI;EACA;;;ApC1HN;EoC8HE;IAEO;;;AAIX;EACI;EACA;EACA;EACA;EACA;;;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ApCnHF;EoCwGE;IAaI;IACA;IACA;;;ApClJN;EoCmIE;IAkBI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EAEA;EACA;EACA;;;AACA;EACI;EACA;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;;;AACA;EACI;;;AAGR;ACvOA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;;;AAEJ;EACI;;;ArC6CN;EqC9CE;IAIQ;;;ArCmCV;EqCvCE;IAOQ;;;ArCkBV;EqCzBE;IAUQ;;;ArCQV;EqClBE;IAaQ;;;ArCDV;EqCZE;IAgBQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ArCVN;EqCCE;IAWQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;;;ArCNN;EqCCE;IAOQ;;;ArCtBV;EqCeE;IAUQ;;;AAGR;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGR;EACI;EACA;;;ArC/BN;EqC6BE;IAIQ;;;ArCxCV;EqCoCE;IAOQ;;;ArClDV;EqC2CE;IAUQ;;;ArC5DV;EqCkDE;IAaQ;;;ArCrEV;EqCwDE;IAgBQ;;;AAGR;EACI;EACA;EACA;;;ArCnDN;EqCgDE;IAKQ;;;ArC5DV;EqCuDE;IAQQ;;;ArCtEV;EqC8DE;IAWQ;;;ArChFV;EqCqEE;IAcQ;;;ArCzFV;EqC2EE;IAiBQ;;;AAGR;EACI;EACA;EACA;;;ArCrFN;EqCkFE;IAKQ;;;AAEJ;EAEI;EACA;EACA;EACA;EACA;;;ArC/FV;EqCyFM;IAQQ;;;AAEJ;EACI;EACA;;;AAKhB;AC/IA;AACA;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;;;AAGJ;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;;AtC0BF;EsChCF;IAQQ;;;AtCUN;EsClBF;IAWQ;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EAEA;;;AAEJ;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;;;AtCZN;EsCIE;IAUO;IACA;IACA;;;AAEH;EACI;;;AAGR;EACI;EACA;;;AAIJ;EACI;EACA;;;AtChBN;EsCcE;IAIQ;;;AtChCV;EsC4BE;IAOQ;;;AAIZ;ACrEA;AACA;EACI;EACA;EAEH;;;AvCuCC;EuC3CF;IAMI;IACA;;;AvCSF;EuChBF;IAUI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AvCoBF;EuC9BF;IAYQ;IACA;;;AvCUN;EuCvBF;IAgBQ;IACA;;;AvCDN;EuChBF;IAoBQ;IACA;;;AvCXN;EuCVF;IAwBQ;;;AvCrBN;EuCHF;IA2BQ;IACA;;;AAEJ;EACI;;;AAEJ;EACI;EACA;;;AvCnBN;EuCiBE;IAIQ;;;AAGR;EACI;EACA;EACA;;;AAGR;AC5DA;AACA;EACI;EACA;;;AACA;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAGR;ACjBA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AzCkDF;EyCzDF;IASQ;;;AzCyCN;EyClDF;IAYQ;;;AzCiBN;EyC7BF;IAeQ;;;AAEJ;EACI;;;AAEI;EACI;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AzCsBN;EyC/BE;IAWQ;IACA;;;AzCYV;EyCxBE;IAeQ;IACA;IACA;;;AAGR;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AzCTN;EyCEE;IASQ;;;AzChCV;EyCuBE;IAYQ;;;AAIZ;ACrEA;AACA;EACI;EACA;;;AAEI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;A1CkCV;E0C9CM;IAcQ;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;;;A1CsBN;E0C5BE;IAQQ;;;AAIZ;EACI;EACA;;;AACA;EACI;;;AAEL;EACC;EACA;EACA;EACA;EACA;;;A1CKF;E0CVC;IAOK;IACA;;;AAML;EACC;EACA;EACA;EACA;;;AAIJ;AC/DA;AAEA;EACQ;EACA;;;AACA;EACI;EACA;EACA;EACA;;;AACA;EACI;EACA;;;AAEJ;EACI;;;A3CsBd;E2CnBM;IAEQ;;;AAGR;EACI;EACA;EACA;EACA;;;AACA;EACI;EACA;;;AAIhB;EACI;;;AACA;EACG;EACA;;;AACA;EACC;;;AAMR;AC7CA;EACI;EACA;EACA;;;AAEA;EACI;EACA;;;A5CoCN;E4CtCE;IAIQ;;;A5C2BV;E4C/BE;IAOQ;;;AAMR;EACI;;;A5CiBN;E4ClBE;IAGQ;;;A5CQV;E4CXE;IAMQ;;;AAGR;EACI;EACA;;;AAEJ;EACI;;;AAIR;EAEI;EACA;;;A5CHF;E4CAF;IAKQ;;;A5CZN;E4COF;IAQQ;;;AAEJ;EAEG;EACA;;;AACA;EACC;;;AAKR;AAEA;AAEI;EACE;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;AACA;EACI;EACA;EACA;EACA;;;AAEJ;AAOA;AC/FA;AAEI;EACI;EACA;EACA;;;A7CuCN;E6C1CE;IAKQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;A7CyBV;E6CnCM;IAYQ;;;AAIZ;EACI;EACA;;;A7CUN;E6CZE;IAIQ;;;AAGR;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;E7CsDN;;;AAvFA;E6CwBE;IAYQ;IACA;I7C6BV;;;A6CzBE;EACI;EACA;;;A7C3CN;E6CyCE;IAIQ;IACA;;;AAGR;EACI;EACA;;;AAEJ;EACI;;;AAGR;AAGA;AACA;EACI;EACA;EACA;EACA;;;AACA;EACI;EACA;EACA;;;AAKR;EAGI;;;A7C7EF;E6C0EF;IAKQ;;;A7CtFN;E6CiFF;IAQQ;;;AAGR;ACzHA;EACI;EACA;;;A9CkCF;E8CpCF;IAIQ;;;AAGR;EACI;EACA;;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;A9CuBN;E8CjCE;IAYQ;;;AAGR;EACE;EACA;EACA;EACA;EACA;;;A9CaJ;E8ClBE;IAOI;;;AAMR;EACI;EAEA;EACA;EACA;;;A9COF;E8CZF;IAOQ;;;A9CFN;E8CLF;IAUQ;;;A9CZN;E8CEF;IAaQ;;;A9C5BN;E8CeF;IAgBQ;;;AAMA;EADJ;IAEQ;;;AAMR;EATJ;IAUQ;IACA;IACA;IACA;;;EACA;IACI;IACA;IACA;;;A9ClBV;E8CCF;IAqBQ;;;A9C7BN;E8CQF;IAwBQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;A9CpCF;E8C4BF;IAUQ;;;A9C7CN;E8CmCF;IAaQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;A9C3DF;E8CmDF;IAUQ;;;AAIJ;EACI;;;AAIJ;EACI;;;AAGR", "file": "main.css", "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap');\r\n\r\n/* ========================= Css Variables Start ======================== */\r\n  :root {    \r\n    // Font Family\r\n    --heading-font: 'Rajdhani', sans-serif;\r\n    --body-font: 'Roboto', sans-serif;\r\n\r\n    // TypoGraphy\r\n    --heading-one: clamp(1.5rem, 2.8vw + 1rem, 3.625rem); //Min:24px - Max:58px\r\n    --heading-two: clamp(1.375rem, 1.7vw + 1rem, 2.5rem); //Min: 22px - Max: 40px\r\n    --heading-three: clamp(1.25rem, .8vw + 1rem, 1.75rem); //Min: 20px - Max: 28px\r\n    --heading-four: clamp(1.125rem, .5vw + 1rem, 1.5rem); //in: 18px - Max: 24px\r\n    --heading-five: clamp(1.125rem, .3vw + 1rem, 1.25rem); //in: 17px - Max: 20px\r\n    --heading-six: 1rem;\r\n  \r\n /* ========================= Color Variables Start =========================== */\r\n     // White Color \r\n     --white: 0 0% 100%;\r\n     \r\n    //Light Color\r\n    --light-h: 0;\r\n    --light-s: 0%;\r\n    --light-l: 78%;\r\n    --light: var(--light-h) var(--light-s) var(--light-l);\r\n    \r\n    // Black Color  \r\n    --black-h: 0;\r\n    --black-s: 0%;\r\n    --black-l: 0%;\r\n    --black: var(--black-h) var(--black-s) var(--black-l);\r\n    \r\n    --heading-color: var(--white);\r\n    --body-color: var(--light);\r\n    --border-color: 0 0% 88%;\r\n    --body-background: 242 63% 12%;\r\n    --section-bg: 240 55% 17%;\r\n  \r\n    /* ================================ Box Shadow Start =============================== */\r\n    // Header Box shadow\r\n    --header-box-shadow: 0px -1px 15px 3px hsl(var(--black) /.3); \r\n   // Mobile bottom fix menu box shadow \r\n    --mobile-box-shadow: 0px -1px 5px 0px hsl(var(--black) /.92);\r\n    // Card box shadow\r\n    --box-shadow: 0px 2px 15px hsl(var(--black) /.05);\r\n    /* ================================ Box Shadow End =============================== */\r\n    \r\n    --base-gradient: linear-gradient(90deg, hsl(var( --base-two)) 36.85%,  hsl(var(--base)) 60.47%);\r\n    --banner-gradient: linear-gradient(180deg, #020333 0%, #13265E 54.69%, #B97169 100%);\r\n    --railway-gradient: linear-gradient(359.96deg, #020333 -107.04%, #13265E 103.76%, #B97169 278.41%);\r\n    --profit-gradient: linear-gradient(180deg, #1E296D -23.97%, #1E296D 92.31%);\r\n    --background-gradient: linear-gradient(180deg, #3803AF 9.03%, #5527BD 71.37%);\r\n    /*============= dashboard gradient background color =========== */\r\n    --dashboard: linear-gradient(185.41deg, #2A88DF 29.7%, #085FB7 65%);\r\n    --dashboard-one: linear-gradient(185.41deg, #FB6581 29.7%, #FA4D6A 65%);\r\n    --dashboard-two: linear-gradient(185.41deg, #F28554 29.7%, #F26D31 65%);\r\n    --dashboard-three: linear-gradient(185.41deg, #8852E4 29.7%, #5712CB 65%);\r\n    --dashboard-four: linear-gradient(185.41deg, #00FCFD 29.7%, #03C2C5 65%);\r\n    --dashboard-five: linear-gradient(185.41deg, #F456A4 29.7%, #E90475 65%);\r\n    /*============= dashboard gradient background color =========== */\r\n  \r\n    /* ========================= Base Color ============================= */\r\n    --base-h: 258;\r\n    --base-s: 97%;\r\n    --base-l: 35%;\r\n    --base: var(--base-h) var(--base-s) var(--base-l);\r\n    // Base Darken \r\n    --base-d-100: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.1);\r\n    --base-d-200: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.2);\r\n    --base-d-300: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.3);\r\n    --base-d-400: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.4);\r\n    --base-d-500: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.5);\r\n    --base-d-600: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.6);\r\n    --base-d-700: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.7);\r\n    --base-d-700: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.7);\r\n    --base-d-800: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.8);\r\n    --base-d-800: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.8);\r\n    --base-d-900: var(--base-h) var(--base-s) calc(var(--base-l) - var(--base-l) * 0.9);\r\n\r\n   /* Base Lighten */\r\n    --base-l-100: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.1);\r\n    --base-l-200: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.2);\r\n    --base-l-300: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.3);\r\n    --base-l-400: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.4);\r\n    --base-l-500: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.5);\r\n    --base-l-600: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.6);\r\n    --base-l-700: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.7);\r\n    --base-l-800: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.8);\r\n    --base-l-900: var(--base-h) calc(var(--base-s)) calc(var(--base-l) + (100% - var(--base-l)) * 0.9);\r\n\r\n     /* ========================= Base Two Color =============================  */\r\n     --base-two-h: 258;\r\n     --base-two-s: 66%;\r\n     --base-two-l: 45%;\r\n     --base-two: var(--base-two-h) var(--base-two-s) var(--base-two-l);\r\n       /* Base Two Darken */\r\n      --base-two-d-100: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.05);\r\n      --base-two-d-200: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.1);\r\n      --base-two-d-300: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.2);\r\n      --base-two-d-400: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.3);\r\n      --base-two-d-500: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.4);\r\n      --base-two-d-600: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.5);\r\n      --base-two-d-700: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.6);\r\n      --base-two-d-800: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.7);\r\n      --base-two-d-900: var(--base-two-h) var(--base-two-s) calc(var(--base-two-l) - var(--base-two-l) * 0.8);\r\n  \r\n    /* Base Two Lighten */\r\n      --base-two-l-100: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.05);\r\n      --base-two-l-200: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.1);\r\n      --base-two-l-300: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.2);\r\n      --base-two-l-400: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.3);\r\n      --base-two-l-500: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.4);\r\n      --base-two-l-600: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.5);\r\n      --base-two-l-700: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.6);\r\n      --base-two-l-800: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.7);\r\n      --base-two-l-900: var(--base-two-h) calc(var(--base-two-s)) calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.8);\r\n        \r\n    // background: linear-gradient(90deg, hsl(258, 66%, 45%) 36.85%, hsl(258, 97%, 35%) 60.47%);\r\n\r\n   \r\n   \r\n    //Dark Color\r\n    --dark-h: 226;\r\n    --dark-s: 28%;\r\n    --dark-l: 15%;\r\n    --dark: var(--dark-h) var(--dark-s) var(--dark-l);\r\n\r\n    --dark-d-100: var(--dark-h) var(--dark-s) calc(var(--dark-l) - var(--dark-l) * 0.1);\r\n    --dark-d-200: var(--dark-h) var(--dark-s) calc(var(--dark-l) - var(--dark-l) * 0.2);\r\n    --dark-d-300: var(--dark-h) var(--dark-s) calc(var(--dark-l) - var(--dark-l) * 0.3);\r\n    --dark-d-400: var(--dark-h) var(--dark-s) calc(var(--dark-l) - var(--dark-l) * 0.4);\r\n    --dark-d-500: var(--dark-h) var(--dark-s) calc(var(--dark-l) - var(--dark-l) * 0.5);\r\n    --dark-d-600: var(--dark-h) var(--dark-s) calc(var(--dark-l) - var(--dark-l) * 0.6);\r\n    --dark-l-100: var(--dark-h) calc(var(--dark-s)) calc(var(--dark-l) + (100% - var(--dark-l)) * 0.1);\r\n    --dark-l-200: var(--dark-h) calc(var(--dark-s)) calc(var(--dark-l) + (100% - var(--dark-l)) * 0.2);\r\n    --dark-l-300: var(--dark-h) calc(var(--dark-s)) calc(var(--dark-l) + (100% - var(--dark-l)) * 0.3);\r\n    --dark-l-400: var(--dark-h) calc(var(--dark-s)) calc(var(--dark-l) + (100% - var(--dark-l)) * 0.4);\r\n    --dark-l-500: var(--dark-h) calc(var(--dark-s)) calc(var(--dark-l) + (100% - var(--dark-l)) * 0.5);\r\n    --dark-l-600: var(--dark-h) calc(var(--dark-s)) calc(var(--dark-l) + (100% - var(--dark-l)) * 0.6);\r\n\r\n   /* ============================== Bootstrap Modifier Start ============================== */\r\n    /* Primary Color */\r\n    --primary-h: 211;\r\n    --primary-s: 100%;\r\n    --primary-l: 50%;\r\n    --primary: var(--primary-h) var(--primary-s) var(--primary-l);\r\n    /* Primary Darken */\r\n    --primary-d-100: var(--primary-h) var(--primary-s) calc(var(--primary-l) - var(--primary-l) * 0.1);\r\n    --primary-d-200: var(--primary-h) var(--primary-s) calc(var(--primary-l) - var(--primary-l) * 0.2);\r\n    --primary-d-300: var(--primary-h) var(--primary-s) calc(var(--primary-l) - var(--primary-l) * 0.3);\r\n    --primary-d-400: var(--primary-h) var(--primary-s) calc(var(--primary-l) - var(--primary-l) * 0.4);\r\n    --primary-d-500: var(--primary-h) var(--primary-s) calc(var(--primary-l) - var(--primary-l) * 0.5);\r\n    /* primary Lighten */\r\n    --primary-l-100: var(--primary-h) calc(var(--primary-s)) calc(var(--primary-l) + (100% - var(--primary-l)) * 0.1);\r\n    --primary-l-200: var(--primary-h) calc(var(--primary-s)) calc(var(--primary-l) + (100% - var(--primary-l)) * 0.2);\r\n    --primary-l-300: var(--primary-h) calc(var(--primary-s)) calc(var(--primary-l) + (100% - var(--primary-l)) * 0.3);\r\n    --primary-l-400: var(--primary-h) calc(var(--primary-s)) calc(var(--primary-l) + (100% - var(--primary-l)) * 0.4);\r\n    --primary-l-500: var(--primary-h) calc(var(--primary-s)) calc(var(--primary-l) + (100% - var(--primary-l)) * 0.5);\r\n    --primary-l-600: var(--primary-h) calc(var(--primary-s)) calc(var(--primary-l) + (100% - var(--primary-l)) * 0.6);\r\n\r\n    /* Secondary Color */\r\n    --secondary-h: 210;\r\n    --secondary-s: 11%;\r\n    --secondary-l: 71%;\r\n    --secondary: var(--secondary-h) var(--secondary-s) var(--secondary-l);\r\n    /* Secondary Darken */\r\n    --secondary-d-100: var(--secondary-h) var(--secondary-s) calc(var(--secondary-l) - var(--secondary-l) * 0.1);\r\n    --secondary-d-200: var(--secondary-h) var(--secondary-s) calc(var(--secondary-l) - var(--secondary-l) * 0.2);\r\n    --secondary-d-300: var(--secondary-h) var(--secondary-s) calc(var(--secondary-l) - var(--secondary-l) * 0.3);\r\n    --secondary-d-400: var(--secondary-h) var(--secondary-s) calc(var(--secondary-l) - var(--secondary-l) * 0.4);\r\n    --secondary-d-500: var(--secondary-h) var(--secondary-s) calc(var(--secondary-l) - var(--secondary-l) * 0.5);   \r\n    /* secondary Lighten */\r\n    --secondary-l-100: var(--secondary-h) calc(var(--secondary-s)) calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.1);\r\n    --secondary-l-200: var(--secondary-h) calc(var(--secondary-s)) calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.2);\r\n    --secondary-l-300: var(--secondary-h) calc(var(--secondary-s)) calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.3);\r\n    --secondary-l-400: var(--secondary-h) calc(var(--secondary-s)) calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.4);\r\n    --secondary-l-500: var(--secondary-h) calc(var(--secondary-s)) calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.5);\r\n    --secondary-l-600: var(--secondary-h) calc(var(--secondary-s)) calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.6);\r\n\r\n   /* Success Color */\r\n    --success-h: 115;\r\n    --success-s: 100%;\r\n    --success-l: 47%;\r\n    --success: var(--success-h) var(--success-s) var(--success-l);\r\n    /* Success Darken */\r\n    --success-d-100: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.1);\r\n    --success-d-200: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.2);\r\n    --success-d-300: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.3);\r\n    --success-d-400: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.4);\r\n    --success-d-500: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.5);\r\n    /* Success Lighten */\r\n    --success-l-100: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.1);\r\n    --success-l-200: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.2);\r\n    --success-l-300: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.3);\r\n    --success-l-400: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.4);\r\n    --success-l-500: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.5);\r\n    --success-l-600: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.6);\r\n\r\n    /* Danger Color */\r\n    --danger-h: 0;\r\n    --danger-s: 96%;\r\n    --danger-l: 63%;\r\n    --danger: var(--danger-h) var(--danger-s) var(--danger-l);\r\n    /* Danger Darken */\r\n    --danger-d-100: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.1);\r\n    --danger-d-200: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.2);\r\n    --danger-d-300: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.3);\r\n    --danger-d-400: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.4);\r\n    --danger-d-500: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.5);\r\n    /* danger Lighten */\r\n    --danger-l-100: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.1);\r\n    --danger-l-200: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.2);\r\n    --danger-l-300: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.3);\r\n    --danger-l-400: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.4);\r\n    --danger-l-500: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.5);\r\n    --danger-l-600: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.6);\r\n\r\n    /* Warning Color */\r\n    --warning-h: 29;\r\n    --warning-s: 88%;\r\n    --warning-l: 59%;\r\n    --warning: var(--warning-h) var(--warning-s) var(--warning-l);\r\n    /* Warning Darken */\r\n    --warning-d-100: var(--warning-h) var(--warning-s) calc(var(--warning-l) - var(--warning-l) * 0.1);\r\n    --warning-d-200: var(--warning-h) var(--warning-s) calc(var(--warning-l) - var(--warning-l) * 0.2);\r\n    --warning-d-300: var(--warning-h) var(--warning-s) calc(var(--warning-l) - var(--warning-l) * 0.3);\r\n    --warning-d-400: var(--warning-h) var(--warning-s) calc(var(--warning-l) - var(--warning-l) * 0.4);\r\n    --warning-d-500: var(--warning-h) var(--warning-s) calc(var(--warning-l) - var(--warning-l) * 0.5);\r\n    /* Warning Lighten */\r\n    --warning-l-100: var(--warning-h) calc(var(--warning-s)) calc(var(--warning-l) + (100% - var(--warning-l)) * 0.1);\r\n    --warning-l-200: var(--warning-h) calc(var(--warning-s)) calc(var(--warning-l) + (100% - var(--warning-l)) * 0.2);\r\n    --warning-l-300: var(--warning-h) calc(var(--warning-s)) calc(var(--warning-l) + (100% - var(--warning-l)) * 0.3);\r\n    --warning-l-400: var(--warning-h) calc(var(--warning-s)) calc(var(--warning-l) + (100% - var(--warning-l)) * 0.4);\r\n    --warning-l-500: var(--warning-h) calc(var(--warning-s)) calc(var(--warning-l) + (100% - var(--warning-l)) * 0.5);\r\n    --warning-l-600: var(--warning-h) calc(var(--warning-s)) calc(var(--warning-l) + (100% - var(--warning-l)) * 0.6);\r\n\r\n    /* Info Color */\r\n    --info-h: 196;\r\n    --info-s: 100%;\r\n    --info-l: 50%;\r\n    --info: var(--info-h) var(--info-s) var(--info-l);\r\n    /* Info Darken */\r\n    --info-d-100: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.1);\r\n    --info-d-200: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.2);\r\n    --info-d-300: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.3);\r\n    --info-d-400: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.4);\r\n    --info-d-500: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.5);\r\n    /* Info Lighten */\r\n    --info-l-100: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.1);\r\n    --info-l-200: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.2);\r\n    --info-l-300: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.3);\r\n    --info-l-400: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.4);\r\n    --info-l-500: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.5);\r\n    --info-l-600: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.6);\r\n\r\n   /* Violet Color */\r\n    --violet-h: 251;\r\n    --violet-s: 96%;\r\n    --violet-l: 63%;\r\n    --violet: var(--violet-h) var(--violet-s) var(--violet-l);\r\n    /* Violet Darken */\r\n    --violet-d-100: var(--violet-h) var(--violet-s) calc(var(--violet-l) - var(--violet-l) * 0.1);\r\n    --violet-d-200: var(--violet-h) var(--violet-s) calc(var(--violet-l) - var(--violet-l) * 0.2);\r\n    --violet-d-300: var(--violet-h) var(--violet-s) calc(var(--violet-l) - var(--violet-l) * 0.3);\r\n    --violet-d-400: var(--violet-h) var(--violet-s) calc(var(--violet-l) - var(--violet-l) * 0.4);\r\n    --violet-d-500: var(--violet-h) var(--violet-s) calc(var(--violet-l) - var(--violet-l) * 0.5);\r\n    /* Violet Lighten */\r\n    --violet-l-100: var(--violet-h) calc(var(--violet-s)) calc(var(--violet-l) + (100% - var(--violet-l)) * 0.1);\r\n    --violet-l-200: var(--violet-h) calc(var(--violet-s)) calc(var(--violet-l) + (100% - var(--violet-l)) * 0.2);\r\n    --violet-l-300: var(--violet-h) calc(var(--violet-s)) calc(var(--violet-l) + (100% - var(--violet-l)) * 0.3);\r\n    --violet-l-400: var(--violet-h) calc(var(--violet-s)) calc(var(--violet-l) + (100% - var(--violet-l)) * 0.4);\r\n    --violet-l-500: var(--violet-h) calc(var(--violet-s)) calc(var(--violet-l) + (100% - var(--violet-l)) * 0.5);\r\n    --violet-l-600: var(--violet-h) calc(var(--violet-s)) calc(var(--violet-l) + (100% - var(--violet-l)) * 0.6);\r\n\r\n    /* Yellow Color */\r\n    --yellow-h: 50;\r\n    --yellow-s: 100%;\r\n    --yellow-l: 50%;\r\n    --yellow: var(--yellow-h) var(--yellow-s) var(--yellow-l);\r\n    /* Yellow Darken */\r\n    --yellow-d-100: var(--yellow-h) var(--yellow-s) calc(var(--yellow-l) - var(--yellow-l) * 0.1);\r\n    --yellow-d-200: var(--yellow-h) var(--yellow-s) calc(var(--yellow-l) - var(--yellow-l) * 0.2);\r\n    --yellow-d-300: var(--yellow-h) var(--yellow-s) calc(var(--yellow-l) - var(--yellow-l) * 0.3);\r\n    --yellow-d-400: var(--yellow-h) var(--yellow-s) calc(var(--yellow-l) - var(--yellow-l) * 0.4);\r\n    --yellow-d-500: var(--yellow-h) var(--yellow-s) calc(var(--yellow-l) - var(--yellow-l) * 0.5);\r\n    /* yellow Lighten */\r\n    --yellow-l-100: var(--yellow-h) calc(var(--yellow-s)) calc(var(--yellow-l) + (100% - var(--yellow-l)) * 0.1);\r\n    --yellow-l-200: var(--yellow-h) calc(var(--yellow-s)) calc(var(--yellow-l) + (100% - var(--yellow-l)) * 0.2);\r\n    --yellow-l-300: var(--yellow-h) calc(var(--yellow-s)) calc(var(--yellow-l) + (100% - var(--yellow-l)) * 0.3);\r\n    --yellow-l-400: var(--yellow-h) calc(var(--yellow-s)) calc(var(--yellow-l) + (100% - var(--yellow-l)) * 0.4);\r\n    --yellow-l-500: var(--yellow-h) calc(var(--yellow-s)) calc(var(--yellow-l) + (100% - var(--yellow-l)) * 0.5);\r\n    --yellow-l-600: var(--yellow-h) calc(var(--yellow-s)) calc(var(--yellow-l) + (100% - var(--yellow-l)) * 0.6);\r\n    /* ============================== Bootstrap Modifier End ============================== */\r\n  }\r\n/* ========================= Css Variables End =========================== */", "// ============================ Media Breakpoint for Each Device Start ============================ \r\n//Mobile Landscape\r\n@mixin landscape {\r\n  @media all and (orientation:landscape) {\r\n    @content;\r\n  }\r\n}\r\n\r\n//Mobile Potrait\r\n@mixin potrait {\r\n  @media all and (orientation:potrait) {\r\n    @content;\r\n  }\r\n}\r\n  \r\n//Extra Extra Small Screen\r\n@mixin xxsm-screen {\r\n  @media screen and (max-width: 374px) {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Xtra Small\r\n@mixin xsm-screen {\r\n  @media screen and (max-width: 424px) {\r\n    @content;\r\n  }\r\n}\r\n// Medium Small Screen (max-width: 575)\r\n@mixin msm-screen {\r\n  @media screen and (max-width: 575px) {\r\n    @content;\r\n  }\r\n}\r\n\r\n//Small Screen (max-width: 767)\r\n@mixin sm-screen {\r\n  @media screen and (max-width: 767px) {\r\n    @content;\r\n  }\r\n}\r\n\r\n//Large Screen (max-width: 991)\r\n@mixin md-screen {\r\n  @media screen and (max-width: 991px) {\r\n    @content;\r\n  }\r\n}\r\n\r\n//Xtra Large Screen (max-width: 1199)\r\n@mixin lg-screen {\r\n  @media screen and (max-width: 1199px) {\r\n    @content;\r\n  }\r\n}\r\n\r\n//Xtra Large Screen i (max-width: 1399)\r\n@mixin xl-screen {\r\n  @media screen and (max-width: 1399px) {\r\n    @content;\r\n  }\r\n}\r\n\r\n//Xtra Large Screen ii (max-width: 1499)\r\n@mixin xxl-screen {\r\n  @media screen and (max-width: 1499px) {\r\n    @content;\r\n  }\r\n}\r\n//Xtra Large Screen ii (max-width: 1599)\r\n@mixin xxxl-screen {\r\n  @media screen and (max-width: 1599px) {\r\n    @content;\r\n  }\r\n}\r\n/* ============================ Media Breakpoint for Each Device End ============================ */\r\n\r\n/* =========================== Font Size For resposive devices Start =========================== */\r\n@mixin fs-10 {\r\n  font-size: 0.625rem;\r\n}\r\n@mixin fs-11 {\r\n  font-size: 0.6875rem;\r\n}\r\n@mixin fs-12 {\r\n  font-size: 0.75rem;\r\n}\r\n@mixin fs-13 {\r\n  font-size: 0.8125rem;\r\n}\r\n@mixin fs-14 {\r\n  font-size: 0.875rem;\r\n}\r\n@mixin fs-15 {\r\n  font-size: 0.9375rem;\r\n}\r\n@mixin fs-16 {\r\n  font-size: 1rem;\r\n}\r\n@mixin fs-17 {\r\n  font-size: 1.0625rem;\r\n}\r\n@mixin fs-18 {\r\n  font-size: 1.125rem;\r\n}\r\n@mixin fs-19 {\r\n  font-size: 1.1875rem;\r\n}\r\n@mixin fs-20 {\r\n  font-size: 1.25rem;\r\n}\r\n@mixin fs-21 {\r\n  font-size: 1.3125rem;\r\n}\r\n@mixin fs-22 {\r\n  font-size: 2.75rem;\r\n}\r\n@mixin fs-23 {\r\n  font-size: 1.4375rem;\r\n}\r\n@mixin fs-24 {\r\n  font-size: 1.5rem;\r\n}\r\n@mixin fs-25 {\r\n  font-size: 1.5625rem;\r\n}\r\n@mixin fs-26 {\r\n  font-size: 1.625rem;\r\n}\r\n@mixin fs-27 {\r\n  font-size: 1.6875rem;\r\n}\r\n@mixin fs-28 {\r\n  font-size: 1.75rem;\r\n}\r\n@mixin fs-29 {\r\n  font-size: 1.8125rem;\r\n}\r\n@mixin fs-30 {\r\n  font-size: 1.875rem;\r\n}\r\n/* ================================== Font Size For resposive devices End =============================== */", "\r\n/* ================================= Common Typography Css Start =========================== */\r\n*{\r\n    margin: 0;\r\n    padding: 0;\r\n    list-style: none;\r\n    box-sizing: border-box;\r\n}\r\nbody{\r\n    font-family: var(--heading-font);\r\n    color: hsl(var(--body-color));\r\n    word-break: break-word;\r\n    background-color: hsl(var(--body-background));\r\n    min-height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\np{\r\n    font-family: var(--body-font);\r\n    @extend .fs-16; \r\n    margin: 0;\r\n    font-weight: 400;\r\n    word-break: break-word;\r\n}\r\nspan {\r\n  display: inline-block;\r\n}\r\n\r\n  h1,\r\n  h2,\r\n  h3,\r\n  h4,\r\n  h5,\r\n  h6 {\r\n    margin: 0 0 20px 0;\r\n    font-family: var(--heading-font);\r\n    color: hsl(var(--heading-color));\r\n    line-height: 1.3;\r\n    word-break: break-word;\r\n    @include sm-screen {\r\n      margin: 0 0 15px 0;\r\n    }\r\n  }\r\n  h1 {\r\n    font-size: var(--heading-one);\r\n    font-weight: 700;\r\n  }\r\n  h2 {\r\n    font-size: var(--heading-two);\r\n    font-weight: 700;\r\n  }\r\n  h3 {\r\n    font-size: var(--heading-three);\r\n    font-weight: 700;\r\n  }\r\n  h4 {\r\n    font-size: var(--heading-four);\r\n    font-weight: 700;\r\n  }\r\n  h5 {\r\n    font-size: var(--heading-five);\r\n    font-weight: 700;\r\n  }\r\n  h6 {\r\n    font-size: var(--heading-six);\r\n    font-weight: 500;\r\n  }\r\n  h1 > a,\r\n  h2 > a,\r\n  h3 > a,\r\n  h4 > a,\r\n  h5 > a,\r\n  h6 > a {\r\n    font-weight: 600;\r\n    transition: .2s linear;\r\n    line-height: 1.3;\r\n    word-break: break-word;\r\n    color: hsl(var(--heading-color));\r\n  }\r\n\r\na{\r\n    display: inline-block;\r\n    transition: .2s linear;\r\n    text-decoration: none;\r\n    color: hsl(var(--body-color));\r\n}\r\na:hover {\r\n    color: hsl(var(--base));\r\n  }\r\n\r\nul{\r\n    margin: 0;\r\n    padding: 0;\r\n    list-style: none;\r\n}\r\nimg {\r\n    max-width: 100%;\r\n    height: auto;\r\n    vertical-align: middle;\r\n}\r\n  select {\r\n    cursor: pointer;\r\n  }\r\n  ul,\r\n  ol {\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n  }\r\n  *:focus {\r\n    outline: none;\r\n  }\r\n  button {\r\n    cursor: pointer;\r\n    border: none;\r\n    background-color: transparent;\r\n    color: hsl(var(--white));\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n  }\r\n  .form-select:focus {\r\n    outline: 0;\r\n    box-shadow: none;\r\n}\r\n/* ================================= Common Typography Css End =========================== */", "/* ================================= Custom Classes Css Start =========================== */\r\n/* Column Extra Small Screen */\r\n.col-xsm-6 {\r\n    @media screen and (min-width:425px) and (max-width:575px) {\r\n        width: 50%;\r\n    }\r\n}\r\n// Custom Container\r\n.custom--container {\r\n    max-width: 1680px;\r\n    margin: 0 auto;\r\n    padding: 0 15px;\r\n}\r\n\r\n\r\n/* Section Background */\r\n.section-bg {\r\n    background-color: hsl(var(--section-bg));\r\n}\r\n// Full Screen Height Css\r\n.full-display {\r\n    flex-grow: 1;\r\n    flex-shrink: 1;\r\n    flex-basis: auto;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    flex-direction: column;\r\n}\r\n\r\n// Bg Image Css\r\n.bg-img{\r\n    background-size: cover;\r\n    background-repeat: no-repeat;\r\n    background-position: center center;\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n/* Background Ovelray Css Start */\r\n.bg-overlay{\r\n    position: relative;\r\n    isolation: isolate;\r\n    &::before{\r\n        position: absolute;\r\n        content: \"\";\r\n        left: 0;\r\n        top: 0;\r\n        background-color: hsl(var(--base-two)); \r\n        opacity: .8;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: -1;\r\n    }\r\n}\r\n.bg-overlay-two{\r\n    position: relative;\r\n    isolation: isolate;\r\n    &::before{\r\n        position: absolute;\r\n        content: \"\";\r\n        left: 0;\r\n        top: 0;\r\n        background-color: hsl(var(--base)); \r\n        opacity: .8;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: -1;\r\n    }\r\n}\r\n\r\n/* Hide Scroll bar Css For Custom Modal */\r\nbody.scroll-hidden {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    overflow-y: hidden;\r\n    width: calc(100% - 8px);\r\n}\r\nbody.scroll-hidden .header {\r\n    right: 8px;\r\n}\r\n body.scroll-hidden .header.fixed-header {\r\n    padding-right: 8px;\r\n}\r\n\r\n// For Small Device\r\nbody.scroll-hidden-sm{\r\n    @include md-screen {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        overflow-y: hidden;\r\n        width: calc(100% - 0px);\r\n    }\r\n}\r\n/* ================================= Custom Classes Css End =========================== */", "/*=================== Fully Fit image Css ===================*/\r\n.fit-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;  \r\n}\r\n/* ============================= Display Flex Css Start ============================= */\r\n  .flex-wrap {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n  .flex-align {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n  }\r\n  .flex-center {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n  .flex-between {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n/* ============================= Display Flex Css End ============================= */\r\n\r\n/* ===================== Font Size For resposive devices Start =================== */\r\n.fs-10 {\r\n    font-size: 0.625rem;\r\n}\r\n.fs-11 {\r\n    font-size: 0.6875rem;\r\n}\r\n.fs-12 {\r\n    font-size: 0.75rem;\r\n}\r\n.fs-13 {\r\n    font-size: 0.8125rem;\r\n}\r\n.fs-14 {\r\n    font-size: 0.875rem;\r\n}\r\n.fs-15 {\r\n    font-size: 0.9375rem;\r\n}\r\n.fs-16 {\r\n    font-size: 1rem;\r\n    @include msm-screen {\r\n        @include fs-15; \r\n    }\r\n}\r\n.fs-17 {\r\n    font-size: 1.0625rem;\r\n    @include md-screen {\r\n        font-size: 1rem;\r\n    }\r\n    @include msm-screen {\r\n        @include fs-15; \r\n    }\r\n}\r\n.fs-18 {\r\n    font-size: 1.125rem;\r\n    @include md-screen {\r\n        @include fs-16; \r\n    }\r\n    @include msm-screen {\r\n        @include fs-15; \r\n    }\r\n}\r\n/* ===================== Font Size For resposive devices End =================== */\r\n\r\n\r\n/* ============================= Positioning Css Class Start ===================== */\r\n.pa {\r\n    position: absolute;\r\n    content: \"\";\r\n}\r\n.pa-wh-100 {\r\n    position: absolute;\r\n    width: 100%;  \r\n    height: 100%;\r\n}\r\n.pa-tl-0 {\r\n    position: absolute;\r\n    content: \"\";\r\n    top: 0;\r\n    left: 0;\r\n}\r\n.pa-wh-100-tl-0 {\r\n    position: absolute;\r\n    content: \"\";\r\n    width: 100%;  \r\n    height: 100%;\r\n    top: 0;\r\n    left: 0;\r\n}\r\n/* ============================= Positioning Css Class End ===================== */\r\n", "\r\n/* ====================== Section Heading ==================== */\r\n.section-heading {\r\n  margin-bottom: 40px;\r\n  text-align: left;\r\n  position: relative;\r\n  z-index: 1;\r\n  @include msm-screen {\r\n    margin-bottom: 20px;\r\n  }\r\n  &__subtitle {\r\n    margin-bottom: 15px;\r\n    background: (var(--base-gradient));\r\n    font-size: 12px;\r\n    padding: 5px 15px;\r\n    border-radius: 100px;\r\n    font-family: var(--heading-font);\r\n    font-weight: 700;\r\n    letter-spacing: 2px;\r\n    color: hsl(var(--heading-color));\r\n  }\r\n  &__title {\r\n    margin-bottom: 0;\r\n    font-size: var(--heading-one);\r\n  }\r\n  &__desc {\r\n    max-width: 750px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n  }\r\n  // Style Two\r\n  &.style-two {\r\n    text-align: center;\r\n    @include msm-screen {\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n  // Style Three\r\n  &.style-three {\r\n    display: flex;\r\n    justify-content: space-between; \r\n    align-items: center;\r\n    text-align: left;\r\n    @include sm-screen {\r\n      display: block;\r\n    }\r\n    .section-heading {\r\n      &__title {\r\n        margin-bottom: 0;\r\n        padding-bottom: 0;\r\n        margin-right: 50px;\r\n        padding-right: 50px;\r\n        flex-shrink: 0;\r\n        position: relative;\r\n          @include sm-screen {\r\n            margin-bottom: 10px;\r\n            margin-right: 0px;\r\n            padding-right: 0px;\r\n          }\r\n          &::before {\r\n            position: absolute;\r\n            content: \"\";\r\n            left: 100%;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n            width: 2px;\r\n            height: 35px;\r\n            background-color: hsl(var(--base));\r\n            @include sm-screen {\r\n              display: none;\r\n            }\r\n        }\r\n      }\r\n      &__desc {\r\n        max-width: 100%;\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n/* ====================== Section Heading En d==================== */\r\n\r\n/*================= Slick Arrow & Dots css Start ================ */\r\n.slick-initialized.slick-slider {\r\n  margin: 0 -10px;\r\n  .slick-track {\r\n      display: flex;\r\n  }\r\n  .slick-slide {\r\n      height: auto;\r\n      padding: 0 10px;\r\n      > div {\r\n          height: 100%;\r\n      }\r\n  }\r\n}\r\n\r\n.slick-arrow {\r\n  position: absolute;\r\n  z-index: 1;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border: none;\r\n  color: hsl(var(--white));;\r\n  width: 60px;\r\n  height: 60px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-radius: 50%;\r\n  transition: .2s linear; \r\n  background-color: hsl(var(--base));\r\n  color: hsl(var(--white));;\r\n  font-size: 20px;\r\n  @include md-screen {\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: 16px;\r\n  }\r\n  @include msm-screen {\r\n    width: 45px;\r\n    height: 45px;\r\n  }\r\n   &:hover {\r\n    border-color: hsl(var(--base));\r\n    background-color: hsl(var(--base-two));\r\n  }\r\n}\r\n.slick-next {\r\n  right: -20px;\r\n  @include md-screen {\r\n    right: -10px;\r\n  }\r\n  @include msm-screen {\r\n    right: 10px;\r\n  }\r\n}\r\n.slick-prev {\r\n  left: -20px;\r\n  @include md-screen {\r\n    left: -10px;\r\n  }\r\n  @include msm-screen {\r\n    left: 10px;\r\n  }\r\n}\r\n\r\n/* -------------- Slick Slider Arrow Style two ------------ */\r\n.slick-arrow {\r\n  position: absolute;\r\n  z-index: 1;\r\n  border: none;\r\n  background-color: transparent;\r\n  color: hsl(var(--white));;\r\n  width: 32px;\r\n  height: 34px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-radius: 5px;\r\n  transition: .4s;\r\n  background-color:  hsl(var(--dark));\r\n  color: hsl(var(--white));\r\n  top: -67px;\r\n  @include lg-screen {\r\n    top: -69px;\r\n  }\r\n  @include sm-screen {\r\n    top: auto; \r\n    bottom: -50px;\r\n    right: 50%;\r\n  }\r\n}\r\n.slick-arrow:hover {\r\n  background-color: hsl(var(--dark));\r\n  color: hsl(var(--white));;\r\n}\r\n.slick-next {\r\n  right: 10px;\r\n  background-color:  hsl(var(--base));\r\n  @include sm-screen {\r\n    top: auto; \r\n    right: calc(50% + -37px);\r\n\r\n  } \r\n}\r\n.slick-prev {\r\n  right: 52px;\r\n  @include sm-screen {\r\n    top: auto; \r\n    right: calc(50% - -5px);\r\n  }\r\n}\r\n\r\n/*  Dots Css Start */\r\n.slick-dots {\r\n    text-align: center;\r\n    padding-top: 20px;\r\n    li {\r\n        display: inline-block;\r\n        button {\r\n            border: none;\r\n            background-color: hsl(var(--dark));\r\n            color: hsl(var(--white));;\r\n            margin: 0 3px;\r\n            width: 8px;\r\n            height: 8px;\r\n            border-radius: 1px;\r\n            border-radius: 50%;\r\n            text-indent: -9999px;\r\n            transition: .3s linear;\r\n        }\r\n        &.slick-active button {\r\n            background-color: hsl(var(--base));\r\n            width: 25px;\r\n            border-radius: 5px;\r\n        }\r\n    }\r\n}\r\n// Dots Css End\r\n/*================= Slick Arrow & Dots css Start ================ */\r\n\r\n\r\n\r\n", "/* ================================= Background Color Css Start =========================== */\r\n.bg {\r\n    &--base {\r\n        background-color: hsl(var(--base)) !important;\r\n    }\r\n    &--primary {\r\n        background-color: hsl(var(--primary)) !important;\r\n    }\r\n    &--secondary {\r\n        background-color: hsl(var(--secondary)) !important;\r\n    }\r\n    &--success {\r\n        background-color: hsl(var(--success)) !important;\r\n    }\r\n    &--danger {\r\n        background-color: hsl(var(--danger)) !important;\r\n    }\r\n    &--warning {\r\n        background-color: hsl(var(--warning)) !important;\r\n    }\r\n    &--info {\r\n        background-color: hsl(var(--info)) !important;\r\n    }\r\n    &--violet {\r\n        background-color: hsl(var(--violet)) !important;\r\n    }\r\n}\r\n/* ================================= Background Color Css End =========================== */", "/* ================================= Color Css Start =========================== */\r\n.text {\r\n    &--base {\r\n        color: hsl(var(--base)) !important;\r\n    }\r\n    &--base-two {\r\n        color: hsl(var(--base-two)) !important;\r\n    }\r\n    &--primary {\r\n        color: hsl(var(--primary)) !important;;\r\n    }\r\n    &--secondary {\r\n        color: hsl(var(--secondary)) !important;;\r\n    }\r\n    &--success {\r\n        color: hsl(var(--success)) !important;;\r\n    }\r\n    &--danger {\r\n        color: hsl(var(--danger)) !important;;\r\n    }\r\n    &--warning {\r\n        color: hsl(var(--warning)) !important;;\r\n    }\r\n    &--info {\r\n        color: hsl(var(--info)) !important;;\r\n    }\r\n    &--violet {\r\n        color: hsl(var(--violet)) !important;;\r\n    }\r\n}\r\n/* ================================= Color Css End =========================== */", "/* ================================= Margin Css Start =========================== */\r\n.my-120 {\r\n  margin-top: 60px;\r\n  margin-bottom: 60px;\r\n  @media (min-width: 992px) {\r\n   margin-top: 120px;\r\n   margin-bottom: 120px;\r\n}\r\n}\r\n.mt-120 {\r\n  margin-top: 60px;\r\n  @media (min-width: 992px) {\r\n   margin-top: 120px;\r\n}\r\n}\r\n.mb-120 {\r\n  margin-bottom: 60px;\r\n  @media (min-width: 992px) {\r\n   margin-bottom: 120px;\r\n}\r\n}\r\n\r\n.my-60 {\r\n  margin-top: 30px;\r\n  margin-bottom: 30px;\r\n  @media (min-width: 992px) {\r\n   margin-top: 120px;\r\n   margin-bottom: 60px;\r\n}\r\n}\r\n.mt-60 {\r\n  margin-top: 30px;\r\n  @media (min-width: 992px) {\r\n   margin-top: 60px;\r\n}\r\n}\r\n.mb-60 {\r\n  margin-bottom: 30px;\r\n  @media (min-width: 992px) {\r\n   margin-bottom: 60px;\r\n}\r\n}\r\n\r\n.my-80 {\r\n  margin-top: 40px;\r\n  margin-bottom: 40px;\r\n  @media (min-width: 992px) {\r\n  margin-top: 80px;\r\n  margin-bottom: 80px;\r\n  }\r\n}\r\n.mt-80{\r\nmargin-top: 40px;\r\n@media (min-width: 992px) {\r\nmargin-top: 80px;\r\n}\r\n}\r\n.mb-80{\r\nmargin-bottom: 40px;\r\n@media (min-width: 992px) {\r\nmargin-bottom: 80px;\r\n}\r\n}\r\n.my-40 {\r\n  margin-top: 30px;\r\n  margin-bottom: 30px;\r\n@media (min-width: 992px) {\r\nmargin-top: 40px;\r\nmargin-bottom: 40px;\r\n}\r\n}\r\n.mt-40 {\r\nmargin-top: 30px;\r\n@media (min-width: 992px) {\r\nmargin-top: 40px;\r\n}\r\n}\r\n.mb-40 {\r\nmargin-bottom: 30px;\r\n@media (min-width: 992px) {\r\nmargin-bottom: 40px;\r\n  }\r\n}\r\n/* ================================= Margin Css End =========================== */", "/* ================================= padding Css Start =========================== */\r\n.py-120 {\r\n  padding-top: 60px;\r\n  padding-bottom: 60px;\r\n  @media (min-width: 576px) {\r\n    padding-top: 80px;\r\n    padding-bottom: 80px;\r\n  }\r\n  @media (min-width: 992px) {\r\n    padding-top: 120px;\r\n    padding-bottom: 120px;\r\n  }\r\n}\r\n.pt-120 {\r\n    padding-top: 60px;\r\n    @media (min-width: 576px) {\r\n    padding-top: 80px;\r\n  }\r\n    @media (min-width: 992px) {\r\n    padding-top: 120px;\r\n  }\r\n}\r\n.pb-120 {\r\n    padding-bottom: 60px;\r\n    @media (min-width: 576px) {\r\n    padding-bottom: 80px;\r\n  }\r\n    @media (min-width: 992px) {\r\n    padding-bottom: 120px;\r\n  }\r\n}\r\n.py-60 {\r\n    padding-top: 30px;\r\n    padding-bottom: 30px;\r\n    @media (min-width: 576px) {\r\n    padding-top: 40px;\r\n    padding-bottom: 40px;\r\n  }\r\n    @media (min-width: 992px) {\r\n    padding-top: 60px;\r\n    padding-bottom: 60px;\r\n  }\r\n}\r\n.pt-60 {\r\n    padding-top: 30px;\r\n    @media (min-width: 576px) {\r\n    padding-top: 40px;\r\n  }\r\n    @media (min-width: 992px) {\r\n    padding-top: 60px;\r\n  }\r\n}\r\n  .pb-60 {\r\n    padding-bottom: 30px;\r\n    @media (min-width: 576px) {\r\n    padding-bottom: 40px;\r\n  }\r\n    @media (min-width: 992px) {\r\n    padding-bottom: 60px;\r\n  }\r\n}\r\n\r\n// ========================= Padding Md 60 Bottom =========================== \r\n.pt-md-60 {\r\n     padding-top: 60px;\r\n     @media (min-width: 576px) {\r\n       padding-top: 80px;\r\n     }\r\n     @media (min-width: 992px) {\r\n       padding-top: 60px;\r\n     }\r\n}\r\n  .pb-md-60 {\r\n    padding-bottom: 60px;\r\n    @media (min-width: 576px) {\r\n      padding-bottom: 80px;\r\n    }\r\n    @media (min-width: 992px) {\r\n      padding-bottom: 60px;\r\n    }\r\n}\r\n\r\n// ================================= padding Css End =========================== \r\n", "\r\n/* ================================= Border Color Css Start =========================== */\r\n.border {\r\n    &--base {\r\n        border-color: hsl(var(--base)) !important;\r\n    }\r\n    &--primary {\r\n        border-color: hsl(var(--primary)) !important;\r\n    }\r\n    &--secondary {\r\n        border-color: hsl(var(--secondary)) !important;\r\n    }\r\n    &--success {\r\n        border-color: hsl(var(--success)) !important;\r\n    }\r\n    &--danger {\r\n        border-color: hsl(var(--danger)) !important;\r\n    }\r\n    &--warning {\r\n        border-color: hsl(var(--warning)) !important;\r\n    }\r\n    &--info {\r\n        border-color: hsl(var(--info)) !important;\r\n    }\r\n    &--violet {\r\n        border-color: hsl(var(--violet)) !important;\r\n    }\r\n}\r\n/* ================================= Border Color Css End =========================== */", "/*=========================== Accodion Css start ============================= */\r\n.custom--accordion {\r\n    .accordion-item {\r\n        border: 1px solid hsl(var(--dark) / .09);\r\n        background-color: transparent !important;\r\n        border-radius: 5px;\r\n        overflow: hidden;\r\n        &:not(:last-child) {\r\n            margin-bottom:10px;\r\n        }\r\n    }\r\n    .accordion-body {\r\n        padding: 0;\r\n        background-color: transparent;\r\n        .text {\r\n            max-width: 100%;\r\n            color: hsl(var(--white)/.8);\r\n            @extend .fs-16;\r\n            @include msm-screen {\r\n                max-width: 100%;\r\n            }\r\n        }\r\n    }\r\n    &:first-of-type .accordion-button.collapsed {\r\n        border-radius: 5px;  \r\n    }\r\n    &:last-of-type .accordion-button.collapsed {\r\n        border-radius: 5px;  \r\n    }\r\n    .accordion-button{\r\n        margin-bottom: 10px;\r\n        background-color: hsl(var(--section-bg));\r\n        color: var(--heading-color);\r\n        @extend .fs-18; \r\n        padding: 20px 30px;\r\n        border-radius: 5px;\r\n        font-weight: 700;\r\n        font-family: var(--heading-font);\r\n        @include msm-screen {\r\n            padding: 13px;\r\n            padding-right: 30px;\r\n        }\r\n        &::after {\r\n            background-image: none;\r\n        }\r\n        &:focus {\r\n            box-shadow: none;\r\n        }\r\n        &:not(.collapsed) {\r\n            color: hsl(var(--white)); \r\n            background-color: hsl(var(--section-bg)) !important;\r\n            box-shadow: none;\r\n            &::after {\r\n                transform: rotate(0deg);\r\n                background-image: none;\r\n                color: hsl(var(--base)); ; \r\n            }\r\n        }\r\n        &[aria-expanded=\"true\"], &[aria-expanded=\"false\"] {\r\n            &::after {\r\n                font-family: \"Line Awesome Free\";\r\n                font-weight: 700;\r\n                content: \"\\f068\";\r\n                display: inline-block;\r\n                position: relative;\r\n                margin-left: auto;\r\n                width: 0 !important;\r\n                right: 15px;\r\n                color: hsl(var(--white));\r\n                @include msm-screen {\r\n                    right: -13px;\r\n                    // @extend .fs-14; \r\n                }\r\n            }\r\n        }\r\n        &[aria-expanded=\"false\"] {\r\n            &::after {\r\n                content: \"\\f067\";\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n    \r\n}\r\n/* ================================= Accodion Css End =========================== */", "/* ================================= Button Css Start =========================== */\r\nbutton {\r\n    border: none;\r\n    transition: .2s linear;\r\n    &:focus {\r\n      outline: none;\r\n      box-shadow: none;\r\n    }\r\n}\r\n\r\n.btn {\r\n    &:focus {\r\n        outline: none;\r\n        box-shadow: none;\r\n      }\r\n      color: hsl(var(--white));\r\n      font-weight: 500;\r\n      padding: 10px 30px;\r\n      font-size: 16px;\r\n      border-radius: 4px;\r\n      position: relative;\r\n      z-index: 1;\r\n      text-align: center;\r\n      border: 1px solid transparent;\r\n      font-weight: 600;\r\n      @include sm-screen {\r\n          padding: 11px 25px;\r\n      }\r\n      @include msm-screen {\r\n          padding: 11px 20px;\r\n      }\r\n      &:hover, &:focus {\r\n          color: hsl(var(--white));\r\n          border-color: transparent;\r\n      }\r\n      &--lg {\r\n        padding: 15px 35px;\r\n        @include sm-screen {\r\n            padding: 13px 30px;\r\n        }\r\n        @include msm-screen {\r\n            padding: 11px 25px;\r\n        }\r\n      }\r\n      &--sm {\r\n        padding: 5px 20px;\r\n        @extend .fs-15;\r\n      }\r\n      &--icon {\r\n        width: 35px;\r\n        height: 35px;\r\n        line-height: 35px;\r\n        @extend .fs-15; \r\n        padding: 0;\r\n    }\r\n    /* ============= Different Color Button Start ================== */\r\n    &--base {\r\n        background: var(--base-gradient);\r\n        position: relative;\r\n        z-index: 1;\r\n        overflow: hidden;\r\n        &::before {\r\n            position: absolute;\r\n            left: -100%;\r\n            top: 0;\r\n            height: 100%;\r\n            width: 100%;\r\n            background: var(--base-gradient);\r\n            content: \"\";\r\n            z-index: -1;\r\n            border-radius: 3px;\r\n            transition: .3s ease-in-out;\r\n        }\r\n        &:hover::before {\r\n            left: 0%;\r\n        }\r\n        &::after {\r\n            position: absolute;\r\n            content: \"\";\r\n            top: 0;\r\n            left: 0%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: var(--base-gradient);\r\n            transition: all ease 0.3s;\r\n            z-index: -1;\r\n        }\r\n        &:hover::after {\r\n            left: 100%;\r\n        }\r\n        // &:hover, &:focus {\r\n        //     border: 1px solid hsl(var(--base));\r\n        //     color: hsl(var(--white));\r\n        // }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n         position: relative;\r\n         border: 0;\r\n         background: var(--base-gradient);\r\n         z-index: 1;\r\n         color: hsl(var(--white));\r\n         &::after {\r\n            position: absolute;\r\n            content: \"\";\r\n            width: calc(100% - 4px);\r\n            height: calc(100% - 4px);\r\n            left: 2px;\r\n            top: 2px;\r\n            bottom: -2px;\r\n            right: -2px;\r\n            background: #141443 !important;\r\n            z-index: -1;\r\n            border-radius: inherit;\r\n        }\r\n         &:hover::after{\r\n            background: transparent !important;\r\n\r\n         }\r\n         &::before {\r\n            display: none;\r\n         }\r\n        }\r\n    }\r\n    &--simple {\r\n        font-size: 16px;\r\n        font-family: var(--body-font);\r\n        font-weight: 500;\r\n        color: hsl(var(--white));\r\n        &__icon {\r\n            @extend .fs-13; \r\n            margin-left: 5px;\r\n        }\r\n    }\r\n    &--primary {\r\n        background-color: hsl(var(--primary));\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--primary-d-100)); \r\n        }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n            border: 1px solid hsl(var(--primary));;\r\n            background-color: transparent;\r\n            color: hsl(var(--primary));\r\n            &:hover, &:focus {\r\n                background-color: hsl(var(--primary));;\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n    &--secondary {\r\n        background-color: hsl(var(--secondary));\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--secondary-d-100)); \r\n        }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n            border: 1px solid hsl(var(--secondary));;\r\n            background-color: transparent;\r\n            color: hsl(var(--secondary));\r\n            &:hover, &:focus {\r\n                background-color: hsl(var(--secondary));;\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n    &--success {\r\n        background-color: hsl(var(--success));\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--success-d-100)); \r\n        }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n            border: 1px solid hsl(var(--success));;\r\n            background-color: transparent;\r\n            color: hsl(var(--success));\r\n            &:hover, &:focus {\r\n                background-color: hsl(var(--success));;\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n    &--danger {\r\n        background-color: hsl(var(--danger));\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--danger-d-100)); \r\n        }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n            border: 1px solid hsl(var(--danger));;\r\n            background-color: transparent;\r\n            color: hsl(var(--danger));\r\n            &:hover, &:focus {\r\n                background-color: hsl(var(--danger));;\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n    &--warning {\r\n        background-color: hsl(var(--warning));\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--warning-d-100)); \r\n        }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n            border: 1px solid hsl(var(--warning));;\r\n            background-color: transparent;\r\n            color: hsl(var(--warning));\r\n            &:hover, &:focus {\r\n                background-color: hsl(var(--warning));;\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n    &--info {\r\n        background-color: hsl(var(--info));\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--info-d-100)); \r\n        }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n            border: 1px solid hsl(var(--info));;\r\n            background-color: transparent;\r\n            color: hsl(var(--info));\r\n            &:hover, &:focus {\r\n                background-color: hsl(var(--info));;\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n    &--white {\r\n        background-color: transparent;\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--white)); \r\n        }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n            border: 1px solid hsl(var(--white));\r\n            background-color: transparent;\r\n            color: hsl(var(--white));\r\n            &:hover, &:focus {\r\n                background-color: hsl(var(--white));;\r\n                color: hsl(var(--base));\r\n            }\r\n        }\r\n    }\r\n    &--violet {\r\n        background-color: hsl(var(--violet));\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--violet-d-100)); \r\n        }\r\n        &.pill {\r\n            border-radius: 35px;\r\n        }\r\n        &.outline {\r\n            border: 1px solid hsl(var(--violet));;\r\n            background-color: transparent;\r\n            color: hsl(var(--violet));\r\n            &:hover, &:focus {\r\n                background-color: hsl(var(--violet));;\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n}\r\n/* ================================= Button Css End =========================== */", "\r\n/* ================================= Card Css Start =========================== */\r\n.custom--card {\r\n    border-radius: 5px;\r\n    box-shadow: var(--box-shadow);\r\n    background-color: hsl(var(--white));;\r\n    border: transparent;\r\n    .card-header {\r\n        padding: 13px 20px;\r\n        background-color: transparent;\r\n        border-bottom: 1px solid hsl(var(--dark)/.1);\r\n    }\r\n    .card-body {\r\n        background-color: hsl(var(--white));\r\n        padding: 20px;\r\n        border-radius: 5px;\r\n        &__icon {\r\n            font-size: 26px;\r\n            color: hsl(var(--white));\r\n        }\r\n    }\r\n    .card-footer {\r\n        padding: 13px 20px;\r\n        background-color: transparent;\r\n        border-top: 1px solid hsl(var(--dark)/.1);\r\n    }\r\n}\r\n/* ================================= Card Css End =========================== */\r\n\r\n", "/* ================================= Form Css Start =========================== */\r\n.form--label {\r\n    margin-bottom: 6px;\r\n    @extend .fs-16; \r\n    color: hsl(var(--white) / .6);\r\n    font-weight: 400;\r\n}\r\n\r\n.form-group {\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.form--control {\r\n    border-radius: 5px;\r\n    font-weight: 400;\r\n    outline: none;\r\n    width: 100%;\r\n    padding: 13px 15px;\r\n    font-family: var(--body-font);\r\n    font-size: 16px;\r\n    font-weight: 400;\r\n    background-color: transparent;\r\n    border: 1px solid hsl(var(--white) / .1);\r\n    color: hsl(var(--white)); \r\n    line-height: 1;\r\n    @include xxsm-screen {\r\n        padding: 8px 10px;\r\n    }\r\n    &::placeholder {\r\n        color: hsl(var(--white) / .6);\r\n        @extend .fs-16; \r\n        @include lg-screen {\r\n            font-size: 14px;\r\n        }\r\n        @include xsm-screen {\r\n            font-size: 13px;\r\n        }\r\n    }\r\n    &:focus {\r\n        border-radius: 3px;\r\n        color: hsl(var(--white)); \r\n        box-shadow: none;\r\n        border-color: hsl(var(--base)); \r\n        background-color: transparent; \r\n    }\r\n    &:disabled, &[readonly] {\r\n        background-color: hsl(var(--base)/.3); \r\n        opacity: .6;\r\n        border: 1px solid hsl(var(--body-color)/.1);\r\n    }\r\n    &[type=\"password\"] {\r\n        color: hsl(var(--white)/.5);\r\n        &:focus {\r\n            color: hsl(var(--white)); \r\n        }\r\n    }\r\n    &[type=\"file\"] {\r\n        line-height: 50px;\r\n        padding: 0;\r\n        position: relative;\r\n        &::file-selector-button {\r\n            border: 1px solid hsl(var(--black) / .08);\r\n            padding: 4px 6px;\r\n            border-radius: .2em;\r\n            background-color: hsl(var(--base)) !important;\r\n            transition: .2s linear;\r\n            line-height: 25px;\r\n            position: relative;\r\n            margin-left: 15px;\r\n            color: hsl(var(--body-color)) !important;\r\n            &:hover {\r\n                background-color: hsl(var(--base));\r\n                border: 1px solid hsl(var(--base));\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n}\r\ntextarea.form--control {\r\n    height: 150px;\r\n    padding: 15px;\r\n}\r\n// Autofill Css\r\ninput:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active {\r\n    -webkit-transition: background-color 5000s ease-in-out 0s;\r\n    transition: background-color 5000s ease-in-out 0s;\r\n}\r\ninput:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill {\r\n    -webkit-box-shadow: 0 0 0px 1000px transparent inset;\r\n    -webkit-text-fill-color: hsl(var(--white)) !important;\r\n}\r\n\r\n// input gruop\r\n.input--group {\r\n    position: relative;\r\n}\r\n.form-group {\r\n    position: relative;\r\n    margin-bottom: 16px;\r\n}\r\n.form--check.form-group {\r\n    height: 16px;\r\n}\r\n// Show Hide Password\r\n.password-show-hide {\r\n    position: absolute;\r\n    right: 20px;\r\n    z-index: 3;\r\n    cursor: pointer;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    color: hsl(var(--white)/.5);\r\n}\r\n/* --------------- Number Arrow None --------------------- */\r\n#send-amount input[type='file'] {\r\n    color: hsl(var(--base)); \r\n    cursor: pointer;\r\n}\r\ninput #send-amount::file-selector-button { \r\n    display: none;\r\n}\r\ninput::-webkit-outer-spin-button,\r\ninput::-webkit-inner-spin-button {\r\n    -webkit-appearance: none;\r\n}\r\ninput[type=number]{\r\n    -moz-appearance: textfield;\r\n}\r\n/* ============== Select here ================== */\r\n\r\n// Form Select  \r\n.select {\r\n    color: hsl(var(--white) / .6);\r\n    border: 0;\r\n    position: relative;\r\n    &:focus {\r\n        border: 0;\r\n    }\r\n    option {\r\n        background-color: hsl(var(--dark));\r\n        color: hsl(var(--white));\r\n    }\r\n}\r\n\r\n/* Custom Checkbox Design */\r\n.form--check {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    a {\r\n        display: inline;\r\n    }\r\n    .form-check-input {\r\n        box-shadow: none;\r\n        background-color: transparent;\r\n        box-shadow: none !important;\r\n        border: 0;\r\n        position: relative;\r\n        border-radius: 2px;\r\n        width: 14px;\r\n        height: 14px;\r\n        border: 1px solid hsl(var(--white));\r\n        &:checked {\r\n            background-color: hsl(var(--base)) !important;\r\n            border-color: hsl(var(--base)) !important;\r\n            box-shadow: none;\r\n            &[type=checkbox] {\r\n                background-image:none ;\r\n                }\r\n            &::before {\r\n                position: absolute;\r\n                content: \"\\f00c\";\r\n                font-family: \"Font Awesome 5 Free\";\r\n                font-weight: 900;\r\n                color: hsl(var(--white)); \r\n                @extend .fs-11; \r\n                top: 50%;\r\n                left: 50%;\r\n                transform: translate(-50%, -50%);\r\n            }\r\n        }\r\n    }\r\n    .form-check-label {\r\n        width: calc(100% - 14px);\r\n        padding-left: 8px;\r\n        font-weight: 400;\r\n        font-family: var(--body-font);\r\n    }\r\n    label {\r\n        @include xsm-screen {\r\n            @include fs-15; \r\n        }\r\n    }\r\n    a {\r\n        @include xsm-screen {\r\n            @include fs-15; \r\n        }\r\n    }\r\n}\r\n/* Custom Radion Design */\r\n.form--radio {\r\n    .form-check-input {\r\n        box-shadow: none;\r\n        border: 2px solid hsl(var(--base));\r\n        position: relative;\r\n        margin-right: 10px;\r\n        &:active {\r\n            filter: brightness(100%);\r\n        }\r\n        &:checked {\r\n            background-color: transparent;\r\n            border-color: hsl(var(--base));\r\n            &[type=radio] {\r\n                background-image:none ;\r\n                }\r\n            &::before {\r\n                position: absolute;\r\n                content: \"\";\r\n                top: 50%;\r\n                left: 50%;\r\n                -webkit-transform: translate(-50%, -50%);\r\n                transform: translate(-50%, -50%);\r\n                width: 7px;\r\n                height: 7px;\r\n                background-color: hsl(var(--base));\r\n                border-radius: 50%;\r\n                z-index: 999;\r\n            }\r\n        } \r\n    }\r\n}\r\n/*  Custom Switch Design */\r\n.form--switch {\r\n\t.form-check-input {\r\n\t\twidth: unset;\r\n\t\tborder-radius: 3px;\r\n\t\tbackground-image: none;\r\n\t\tposition: relative;\r\n\t\tbox-shadow: none;\r\n\t\tborder: 0;\r\n\t\tbackground-color: hsl(var(--white)) !important;\r\n\t\tpadding: 10px 20px !important;\r\n\t\tmargin-left: 0;\r\n\t\tmargin-bottom: 5px;\r\n        border-radius: 40px;\r\n        max-width: 0;\r\n\t\t&:focus {\r\n\t\t\twidth: unset;\r\n\t\t\tborder-radius: 40px;\r\n\t\t\tbackground-image: none;\r\n\t\t\tposition: relative;\r\n\t\t\tbox-shadow: none;\r\n\t\t\tborder: 0;\r\n\t\t}\r\n\t\t&::before {\r\n\t\t\tposition: absolute;\r\n\t\t\tcontent: \"\";\r\n\t\t\twidth: 10px;\r\n\t\t\theight: 10px;\r\n\t\t\tbackground-color: hsl(var(--dark)/.6);\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translateY(-50%);\r\n\t\t\tborder-radius: 2px;\r\n\t\t\tleft: 5px;\r\n            border-radius: 50%;\r\n\t\t\ttransition: .2s linear;\r\n\t\t}\r\n\t\t&:checked {\r\n\t\t\tbackground-color: hsl(var(--base)); \r\n            &[type=checkbox] {\r\n                background-image: none;\r\n            }\r\n\t\t\t&::before {\r\n\t\t\t\tleft: calc(100% - 15px);\r\n                background-color: hsl(var(--base));\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n.form-check.form--switch {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\t.form-check-label {\r\n\t\tmargin-right: 10px;\r\n\t\tcolor: hsl(var(--white));\r\n\t\tfont-weight: 500;\r\n\t\tmargin-bottom: 0;\r\n\t\tline-height: 2;\r\n\t}\r\n}\r\n/*  Custom Switch End Design */\r\n/* ----------------------------- Calander Icon -------------- */\r\n::-webkit-calendar-picker-indicator {\r\n    filter: invert(.5);\r\n  }\r\n  ::-webkit-calendar-picker-indicator {\r\n    background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"15\" viewBox=\"0 0 24 24\"><path fill=\"%23bbbbbb\" d=\"M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z\"/></svg>');\r\n  }\r\n/* ================================= Form Css End =========================== */", "/* ================================= Modal Css Start =========================== */\r\n.custom--modal {\r\n    .modal-header {\r\n        border-bottom: 1px solid hsl(var(--white)/0.12);\r\n        padding: 15px;\r\n        &.close {\r\n            width: 35px;\r\n            height: 35px;\r\n            background-color: hsl(var(--danger));\r\n            font-size: 1.5625rem; // 25px\r\n            line-height: 1;\r\n            border-radius: 4px;\r\n            transition: 0.2s linear;\r\n            &:hover {\r\n                background-color: hsl(var(--danger-l-100));\r\n            }\r\n            :focus {\r\n                box-shadow: none;\r\n              }\r\n        } \r\n    }\r\n    .modal-content {\r\n        background-color: hsl(var(--dark));\r\n        border-radius: 10px !important;\r\n    }\r\n   \r\n    .modal-body {\r\n        padding: 15px;\r\n    }\r\n    .modal-icon i {\r\n        font-size: 2rem; // 32px\r\n        color: hsl(var(--base));\r\n        border: 3px solid hsl(var(--base));\r\n        width: 50px;\r\n        height: 50px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        border-radius: 50%;\r\n    }\r\n    .modal-footer {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        flex-shrink: 0;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 15px;\r\n        border-top: 1px solid hsl(var(--white)/0.12);\r\n        justify-content: flex-end;\r\n    }\r\n}\r\n/* ================================= Modal Css End =========================== */\r\n\r\n", "/* ================================= Pagination Css Start =========================== */\r\n.pagination {\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n    margin-top: 60px;\r\n    @include lg-screen {\r\n        margin-top: 50px;\r\n    }\r\n    @include md-screen {\r\n        margin-top: 40px;\r\n    }\r\n    @include sm-screen {\r\n        margin-top: 30px;\r\n    }\r\n    .page-item {\r\n        .page-link{\r\n            border: 1px solid hsl(var(--dark) / .08);\r\n            margin: 0 5px;\r\n            border-radius: 50%;\r\n            height: 50px;\r\n            width: 50px;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            background-color: hsl(var(--section-bg));\r\n            font-weight: 500;\r\n            padding: 0;\r\n            color: hsl(var(--white));\r\n            &.active, &:hover {\r\n                background: var(--background-gradient);\r\n                color: hsl(var(--white)); ;\r\n            }\r\n            &:focus {\r\n                box-shadow: none;\r\n            }\r\n        }\r\n    }\r\n}\r\n/* ================================= Pagination Css End =========================== */", "/* ================================= Table Css Start =========================== */\r\n/* Table Css Start */\r\n.table {\r\n\tmargin: 0;\r\n\t@extend .fs-15; \r\n\tborder-collapse: collapse;\r\n\tborder-collapse: separate;\r\n\tborder-spacing: 0px 20px;\r\n\tposition: relative;\r\n\tz-index: 9;\r\n\t&.style-two {\r\n\t\tborder-spacing: 0px 0px;\r\n\t\tthead { \r\n\t\t\ttr {\r\n\t\t\t\tth {\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tfont-size: 20px; \r\n\t\t\t\t\tpadding: 25px 30px;\r\n\t\t\t\t\tcolor: hsl(var(--white)); \r\n\t\t\t\t\tfont-family: var(--heading-font);\r\n\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\tborder-bottom: 0;\r\n\t\t\t\t\tmax-width: 170px;\r\n\t\t\t\t\t@include md-screen {\r\n\t\t\t\t\t\tfont-size: 17px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:not(:first-child) {\r\n\t\t\t\t\t\tborder-bottom: 0 !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\tborder-radius: 6px 0 0 0px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tborder-radius: 0 6px 0px 0; \r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:nth-child(2) {\r\n\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\ttbody {\r\n\t\t\tborder: 0 !important;\r\n\t\t\ttr {\r\n\t\t\t\tborder-bottom: 1px solid hsl(var(--white) / .08) !important;\r\n\t\t\t\tbackground-color: hsl(var(--section-bg));\r\n\t\t\t\tbox-shadow: none;\r\n\t\t\t\tborder-radius: 6px;\r\n\t\t\t\ttransition: .2s linear;\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\ttd {\r\n\t\t\t\t\t\tborder-bottom: 0 !important;\r\n\t\t\t\t\t\t@include md-screen {\r\n\t\t\t\t\t\t\tborder-bottom: 1px solid hsl(var(--white) / .08) !important;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tborder-radius: 0 0 6px 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\tborder-radius: 0 0 0 6px;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t@include md-screen {\r\n\t\t\t\t\tborder-bottom: 0 !important;\r\n\t\t\t\t}\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\ttransform: translateY(0px);\r\n\t\t\t\t}\r\n\t\t\t\ttd {\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\tpadding: 20px 30px;\r\n\t\t\t\t\tborder-bottom: 1px solid hsl(var(--white) / .08);\r\n\t\t\t\t\tfont-family: var(--body-font);\r\n\t\t\t\t\tcolor: hsl(var(--white)/0.8); \r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tmax-width: 170px;\r\n\t\t\t\t\tfont-size: 20px !important; \r\n\t\t\t\t\t@include xl-screen {\r\n\t\t\t\t\t\tfont-size: 18px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t@include lg-screen {\r\n\t\t\t\t\t\tfont-size: 15px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&::before {\r\n\t\t\t\t\t\tcontent: attr(data-label);\r\n\t\t\t\t\t\tfont-family: var(--heading-font);\r\n\t\t\t\t\t\tfont-size: 20px; \r\n\t\t\t\t\t\tcolor: hsl(var(--white)); \r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tmargin-right: auto;\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t\twidth: 50% !important;\r\n\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\t@extend .fs-18; \r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tthead { \r\n\t\ttr {\r\n\t\t\tbackground: var(--base-gradient);\r\n\t\t\tth {\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tfont-size: 20px; \r\n\t\t\t\tpadding: 25px 30px;\r\n\t\t\t\tcolor: hsl(var(--white)); \r\n\t\t\t\tfont-family: var(--heading-font);\r\n\t\t\t\tfont-weight: 700;\r\n                border-bottom: 0;\r\n\t\t\t\tmax-width: 170px;\r\n\t\t\t\t@include md-screen {\r\n\t\t\t\t\tfont-size: 17px;\r\n\t\t\t\t}\r\n\t\t\t\t&:not(:first-child) {\r\n\t\t\t\t\tborder-left: 0;\r\n\t\t\t\t}\r\n\t\t\t\t&:first-child {\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tborder-radius: 6px 0 0 6px;\r\n\t\t\t\t}\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-radius: 0 6px 6px 0; \r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t\t&:nth-child(2) {\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\ttbody {\r\n\t\tborder: 0 !important;\r\n\t\ttr {\r\n\t\t\tbackground-color: hsl(var(--section-bg));\r\n\t\t\tborder-radius: 6px;\r\n\t\t\ttransition: .2s linear;\r\n\t\t\t&:hover {\r\n\t\t\t\ttransform: translateY(-5px);\r\n\t\t\t}\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: 0;\r\n\t\t\t}\r\n\t\t\ttd {\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tpadding: 20px 30px;\r\n                border: 0;\r\n\t\t\t\tfont-family: var(--body-font);\r\n\t\t\t\tcolor: hsl(var(--white)/0.8); \r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tmax-width: 170px;\r\n\t\t\t\tfont-size: 20px !important; \r\n\t\t\t\t@include md-screen {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tborder-bottom: 1px solid hsl(var(--white) / .08);\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tfont-size: 17px !important;\r\n\t\t\t\t}\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: attr(data-label);\r\n\t\t\t\t\tfont-family: var(--heading-font);\r\n\t\t\t\t\tfont-size: 20px; \r\n\t\t\t\t\tcolor: hsl(var(--white)); \r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tmargin-right: auto;\r\n\t\t\t\t\tdisplay: none;\r\n                    width: 50% !important;\r\n                    text-align: left;\r\n\t\t\t\t}\r\n\t\t\t\t&:first-child {\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t@extend .fs-18; \r\n\t\t\t\t}\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// .table--responsive--md {\r\n//     @include sm-screen {\r\n//         thead {\r\n// \t\t\tdisplay: none;\r\n// \t\t}\r\n// \t\ttbody {\r\n// \t\t\ttr {\r\n// \t\t\t\tdisplay: block;\r\n// \t\t\t\t&:last-child td {\r\n// \t\t\t\t\tborder-bottom: 0;\r\n// \t\t\t\t}\r\n// \t\t\t\ttd {\r\n// \t\t\t\t\tdisplay: flex;\r\n// \t\t\t\t\talign-items: center;\r\n// \t\t\t\t\tjustify-content: space-between;\r\n// \t\t\t\t\tgap: 15px;\r\n// \t\t\t\t\ttext-align: right;\r\n// \t\t\t\t\tpadding: 10px 15px;\r\n// \t\t\t\t\tborder: none;\r\n// \t\t\t\t\tborder-bottom: 1px solid hsl(var(--black)/.8);  \r\n// \t\t\t\t\tmax-width: unset;\r\n// \t\t\t\t\t&:last-child {\r\n// \t\t\t\t\t\tborder: none;\r\n// \t\t\t\t\t}\r\n// \t\t\t\t\t&:first-child {\r\n// \t\t\t\t\t\ttext-align: right;\r\n// \t\t\t\t\t\tborder-left: 0;\r\n// \t\t\t\t\t}\r\n// \t\t\t\t\t&::before {\r\n// \t\t\t\t\t    display: block;\r\n// \t\t\t\t\t\t@include fs-14; \r\n// \t\t\t\t\t\tcolor: hsl(var(--dark)/0.7); \r\n// \t\t\t\t\t}\r\n// \t\t\t\t}\r\n// \t\t\t}\r\n// \t\t}\r\n//     }\r\n// \t@include sm-screen {\r\n//         tbody {\r\n// \t\t\ttr {\r\n// \t\t\t\ttd {\r\n// \t\t\t\t\tborder: 0;\r\n// \t\t\t\t}\r\n// \t\t\t}\r\n// \t\t}\r\n//     }\r\n// }\r\n.table--responsive--lg {\r\n\t\t@include md-screen {\r\n\t\t\tthead {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t\ttbody {\r\n\t\t\t\ttr {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tborder-spacing: 0px 20px;\r\n\t\t\t\t\tborder-radius: 0px;\r\n\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\tborder-radius: 6px 6px 0px 0px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tborder-radius: 0 0 6px 6px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t &:nth-child(odd) {\r\n\t\t\t\t\tbackground-color: hsl(var(--white) /.1);\r\n\t                }\r\n\t\t\t\t\t&:last-child td {\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tborder-bottom: 0 !important;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\ttransform: translateY(-7px);\r\n\t\t\t\t\t}\r\n\t\t\t\t\ttd {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\tgap: 15px;\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t\tpadding: 10px 15px;\r\n\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t\tmax-width: unset;\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t\t\tborder-left: 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&::before {\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tfont-size: 17px; \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t@include md-screen {\r\n\t\t\ttbody {\r\n\t\t\t\ttr {\r\n\t\t\t\t\ttd {\r\n\t\t\t\t\t\tborder-bottom: 1px solid hsl(var(--white) / .08);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n}\r\n/* style- two table css */\r\n.table--responsive--lg {\r\n\t&.style-two {\r\n\t\t@include md-screen {\r\n\t\t\tthead {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t\ttbody {\r\n\t\t\t\ttr {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tborder-spacing: 0px 20px;\r\n\t\t\t\t\tborder-radius: 0px;\r\n\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\tborder-radius: 6px 6px 0px 0px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tborder-radius: 0 0 6px 6px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t &:nth-child(odd) {\r\n\t\t\t\t\tbackground-color: hsl(var(--white) /.1);\r\n\t                }\r\n\t\t\t\t\t&:last-child td {\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tborder-bottom: 0 !important;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\ttransform: translateY(-7px);\r\n\t\t\t\t\t}\r\n\t\t\t\t\ttd {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\tgap: 15px;\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t\tpadding: 10px 15px;\r\n\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t\tmax-width: unset;\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t\t\tborder-left: 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&::before {\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tfont-size: 17px; \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t@include md-screen {\r\n\t\t\ttbody {\r\n\t\t\t\ttr {\r\n\t\t\t\t\ttd {\r\n\t\t\t\t\t\tborder-bottom: 1px solid hsl(var(--white) / .08);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n/*================================= Table Css End =========================== */\r\n\r\n\r\n", "/* ================================= Tab Css Start =========================== */\r\n.custom--tab {\r\n    border-radius: 6px;\r\n    margin-bottom: 50px;\r\n    @include msm-screen {\r\n        margin-bottom: 40px;\r\n    }\r\n    @include xsm-screen {\r\n        margin-bottom: 20px;\r\n    }\r\n    .nav-item {\r\n        border-bottom: 0;\r\n        padding: 5px;\r\n        .nav-link {\r\n            color: hsl(var(--white)); \r\n            padding: 6px 15px !important;\r\n            background-color: hsl(var(--section-bg)/.9) !important;\r\n             border-radius: 5px;\r\n            transition: .4s;\r\n            @extend .fs-16;\r\n            font-weight: 400;\r\n            font-family: var(--body-font);\r\n            border-bottom: 0 !important;\r\n            // border: 1px solid hsl(var(--black) / .08) !important;\r\n            @include lg-screen {\r\n                padding: 4px 15px !important;\r\n            }\r\n            @include xsm-screen {\r\n                padding: 4px 10px !important;\r\n            }\r\n            &.active {\r\n                color: hsl(var(--white)); \r\n                background: var(--base-gradient)!important;\r\n                // border: 1px solid transparent !important;\r\n                &:hover {\r\n                    color: hsl(var(--white)); \r\n                }\r\n            }\r\n            &:hover {\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n}\r\n/* ================================= Tab Css End =========================== */\r\n", "/* ================================= Bandge Css Start =========================== */\r\n.badge{\r\n    border-radius:30px;\r\n    padding: 4px 15px;\r\n    font-size: 0.6875rem; // 11px\r\n    font-weight: 500;\r\n    &--base {\r\n        background-color: hsl(var(--base) / .1) !important; \r\n        border: 1px solid hsl(var(--base)) !important;\r\n        color: hsl(var(--base)) !important;\r\n    }\r\n    &--primary {\r\n        background-color: hsl(var(--primary) / .1) !important; \r\n        border: 1px solid hsl(var(--primary)) !important;\r\n        color: hsl(var(--primary)) !important;\r\n    }\r\n    &--secondary {\r\n        background-color: hsl(var(--secondary) / .1) !important; \r\n        border: 1px solid hsl(var(--secondary)) !important;\r\n        color: hsl(var(--secondary)) !important;\r\n    }\r\n    &--success {\r\n        background-color: hsl(var(--success) / .1) !important; \r\n        border: 1px solid hsl(var(--success)) !important;\r\n        color: hsl(var(--success)) !important;\r\n    }\r\n    &--danger {\r\n        background-color: hsl(var(--danger) / .1) !important; \r\n        border: 1px solid hsl(var(--danger)) !important;\r\n        color: hsl(var(--danger)) !important;\r\n    }\r\n    &--warning {\r\n        background-color: hsl(var(--warning) / .1) !important; \r\n        border: 1px solid hsl(var(--warning)) !important;\r\n        color: hsl(var(--warning)) !important;\r\n    }\r\n    &--info {\r\n        background-color: hsl(var(--info) / .1) !important; \r\n        border: 1px solid hsl(var(--info)) !important;\r\n        color: hsl(var(--info)) !important;\r\n    }\r\n    &--violet {\r\n        background-color: hsl(var(--violet) / .1) !important; \r\n        border: 1px solid hsl(var(--violet)) !important;\r\n        color: hsl(var(--violet)) !important;\r\n    }\r\n}\r\n/* ================================= Bandge Css End =========================== */", "\r\n/* ================= Floting Input Css Start ================= */\r\n// .input--floating {\r\n// \tposition: relative;\r\n// \ta {\r\n// \t\t@extend .fs-15; \r\n// \t\tfont-weight: 500;\r\n// \t\t&:hover {\r\n// \t\t\tcolor: hsl(var(--white));\r\n// \t\t}\r\n// \t}\r\n\r\n// \t.form--control {\r\n// \t\theight: 40px;\r\n// \t\tbackground-color: transparent;\r\n// \t\tborder: 1px solid hsl(var(--base));\r\n// \t\tcolor: hsl(var(--white));\r\n// \t\t@extend .fs-14; \r\n// \t\tpadding-left: 15px;\r\n// \t\twidth: 100%;\r\n// \t\t&:focus {\r\n// \t\t\toutline: none;\r\n// \t\t\tbox-shadow: none;\r\n// \t\t\tbackground-color: transparent;\r\n// \t\t\tborder: 1px solid hsl(var(--base));\r\n// \t\t\toutline: none;\r\n// \t\t\tborder: 1px solid hsl(var(--base));\r\n// \t\t\t~ {\r\n// \t\t\t\t.form--label {\r\n// \t\t\t\t\ttop: 0;\r\n// \t\t\t\t\tleft: 15px;\r\n// \t\t\t\t\tz-index: 5;\r\n// \t\t\t\t\t@extend .fs-12; \r\n// \t\t\t\t\tfont-weight: 500;\r\n// \t\t\t\t\tcolor: hsl(var(--white));\r\n// \t\t\t\t\ttransition: all 0.2s ease-in-out;\r\n// \t\t\t\t}\r\n// \t\t\t}\r\n// \t\t}\r\n// \t\t&::placeholder {\r\n// \t\t\topacity: 0;\r\n// \t\t\tvisibility: hidden;\r\n// \t\t\tcolor: transparent;\r\n// \t\t\topacity: 0;\r\n// \t\t\tvisibility: hidden;\r\n// \t\t\tcolor: transparent;\r\n// \t\t\t@extend .fs-13; \r\n// \t\t}\r\n// \t\t&:not(:placeholder-shown).form--control {\r\n// \t\t\t&:not(:focus) {\r\n// \t\t\t\t~ {\r\n// \t\t\t\t\t.form--label {\r\n// \t\t\t\t\t\ttop: 0;\r\n// \t\t\t\t\t\tleft: 15px;\r\n// \t\t\t\t\t\tz-index: 9;\r\n// \t\t\t\t\t\ttransition: all 0.2s ease-in-out;\r\n// \t\t\t\t\t}\r\n// \t\t\t\t}\r\n// \t\t\t}\r\n// \t\t}\r\n// \t}\r\n// \t.form--label {\r\n// \t\tposition: absolute;\r\n// \t\ttop: 20px;\r\n// \t\tleft: 15px;\r\n// \t\tcursor: text;\r\n// \t\ttransform: translateY(-50%);\r\n// \t\tcolor: hsl(var(--base-two));\r\n// \t\tpadding: 4px 4px;\r\n// \t\t@extend .fs-12; \r\n// \t\tborder-radius: 5px;\r\n// \t\ttransition: 0.2s ease;\r\n// \t\tline-height: 0.8;\r\n// \t\tpointer-events: none;\r\n// \t\tz-index: 1;\r\n// \t\tbackground-color: transparent;\r\n// \t\t&::before {\r\n// \t\t\tposition: absolute;\r\n// \t\t\tcontent: \"\";\r\n// \t\t\twidth: 100%;\r\n// \t\t\theight: 2px;\r\n// \t\t\tleft: 0;\r\n// \t\t\ttop: 53%;\r\n// \t\t\tbackground-color: hsl(var(--base-two));\r\n// \t\t\ttransform: translateY(-50%);\r\n// \t\t\tz-index: -1;\r\n// \t\t}\r\n// \t}\r\n// }\r\n\r\n// select.form-control.form--control:has(option) ~ .form--label {\r\n//     top: 0% !important;\r\n// }\r\n\r\n/* ================= Floting Input Css End ================= */\r\n.form {\r\n\t&-group {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 3rem;\r\n\t\tmargin-bottom: 1.5rem;\r\n\t}\r\n\r\n\t&-label {\r\n\t\tposition: absolute;\r\n\t\tfont-family: inherit;\r\n\t\tfont-size: 1rem;\r\n\t\tfont-weight: 400;\r\n\t\tline-height: inherit;\r\n\t\tleft: 16px;\r\n\t\ttop: 13px;\r\n\t\tpadding: 0 0.25rem;\r\n\t\tcolor:hsl(var(--white)/.8);\r\n\t\tbackground: transparent;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tmargin-bottom: 0 !important;\r\n\t}\r\n\r\n\t&-input {\r\n\t\tposition: absolute;\r\n\t\tfont-family: inherit;\r\n\t\tfont-size: 1rem;\r\n\t\tfont-weight: 400;\r\n\t\tline-height: inherit;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 1;\r\n\t\tresize: none;\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tpadding: 0.75rem 1.25rem;\r\n\t\tborder-radius: 0.25rem;\r\n\t\tborder: 1px solid hsl(var(--white)/.1);\r\n\t\tcolor: hsl(var(--white)/.8);\r\n\t\tbackground: transparent;\r\n\t\ttransition: all 0.2s ease-in-out;\r\n\r\n\t\t&::placeholder {\r\n\t\t\topacity: 0;\r\n\t\t\tvisibility: hidden;\r\n\t\t\tcolor: transparent;\r\n\t\t}\r\n\r\n\t\t&:focus {\r\n\t\t\toutline: none;\r\n\t\t\tborder: 1px solid hsl(var(--base));\r\n\r\n\t\t\t& ~ .form-label {\r\n\t\t\t\ttop: -0.75rem;\r\n\t\t\t\tleft: 1rem;\r\n\t\t\t\tz-index: 5;\r\n\t\t\t\tfont-size: 0.875rem;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor:hsl(var(--white));\r\n\t\t\t\ttransition: all 0.2s ease-in-out;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:not(:placeholder-shown).form-input {\r\n\t\t\t&:not(:focus) {\r\n\t\t\t\t& ~ .form-label {\r\n\t\t\t\t\ttop: -0.75rem;\r\n\t\t\t\t\tleft: 1rem;\r\n\t\t\t\t\tz-index: 9;\r\n\t\t\t\t\tfont-size: 0.875rem;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\ttransition: all 0.2s ease-in-out;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n", "\r\n/* ============= Header Start Here ======================= */\r\n.navbar-brand {\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n    &.logo{\r\n        @include md-screen {\r\n            order: -1;\r\n        }\r\n        img {\r\n            height: 100%;\r\n            width: 100%;\r\n            max-width: 200px;\r\n            max-height: 50px;\r\n            @include lg-screen {\r\n                max-width: 150px;\r\n            }\r\n            @include md-screen {\r\n                max-width: 130px;\r\n            }\r\n            @include msm-screen {\r\n                max-width: 120px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    z-index: 999;\r\n    background-color: transparent;\r\n    @include md-screen {\r\n        top: 0px;\r\n        background-color: hsl(var(--black));\r\n        padding: 10px 0;\r\n        position: absolute;\r\n        left: 0;\r\n        right: 0;\r\n        z-index: 999;\r\n        max-height: 101vh;\r\n        overflow-y: auto;\r\n        &::-webkit-scrollbar {\r\n            width: 5px;\r\n            height: 5px;\r\n        }\r\n        &::-webkit-scrollbar-thumb {\r\n            border-radius: 0px;\r\n        }\r\n    }\r\n    &.fixed-header {\r\n        background-color: hsl(var(--black));\r\n        position: sticky;\r\n        transition: .3s linear;\r\n        top: 0px;\r\n        animation: slide-down .8s;\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n@keyframes slide-down {\r\n    0% {\r\n        opacity: 0;\r\n        transform: translateY( -150%);\r\n    } \r\n    100% {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    } \r\n}\r\n\r\n.navbar {\r\n    padding: 0 !important;\r\n}\r\n/* ========================= Desktop Device Start ========================= */\r\n@media (min-width: 992px) {\r\n.nav-menu {\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n    align-items: center;\r\n    // margin: 0 -25px;\r\n    .nav-item{\r\n        position: relative;\r\n        padding: 0 16px;\r\n        @include xxxl-screen {\r\n            padding: 0 10px;\r\n        }\r\n        @include lg-screen {\r\n            padding: 0 7px;\r\n        }\r\n\r\n        &:hover {\r\n            .nav-link {\r\n                color: hsl(var(--white)) !important;\r\n                &::before {\r\n                    width: 100%;\r\n                }\r\n                .nav-item__icon {\r\n                    transform: rotate(180deg);\r\n                    transition: .2s;\r\n                }\r\n            }\r\n        }\r\n        .nav-link{\r\n            font-weight: 700;\r\n            @include fs-18; \r\n            color: hsl(var(--white)) !important;\r\n            padding: 33px 0;\r\n            position: relative;\r\n            cursor: pointer;\r\n            &.active {\r\n                color: hsl(var(--white)) !important;\r\n                &::before {\r\n                    width: 100%;\r\n                }\r\n            }\r\n            // ======================== Style two ================\r\n            &:hover::before {\r\n                left: 0;\r\n                transition: .3s;\r\n            }\r\n            &::before {\r\n                position: absolute;\r\n                content: \"\";\r\n                right: 0;\r\n                bottom: 25px;\r\n                width: 0;\r\n                height: 2px;\r\n                background-color: hsl(var(--white)); \r\n                // transform: translateX(-50%);\r\n                transition: .3s;\r\n            }\r\n            .nav-item__icon{\r\n                transition: .3s;\r\n                @include fs-13; \r\n                margin-left: 2px;\r\n                @include md-screen {\r\n                    margin-right: 6px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.dropdown-menu {\r\n    display: block;\r\n    visibility: hidden;\r\n    opacity: 0;\r\n    transition: .3s;\r\n    top: 100%;\r\n    left: 0;\r\n    padding: 0 !important;\r\n    transform: scaleY(0);\r\n    transform-origin: top center;\r\n    transition: .3s;\r\n    overflow: hidden;\r\n    border-radius: 0;\r\n    min-width: 190px;\r\n    &__list {\r\n        border-bottom: 1px solid hsl(var(--black) / .08);\r\n        &:last-child {\r\n            border-bottom: 3px solid hsl(var(--base)); \r\n        }\r\n    }\r\n    &__link{\r\n        padding: 7px 20px;\r\n        font-weight: 700;\r\n        @include fs-18; \r\n        transition: .3s;\r\n        &:focus, &:hover {\r\n            color: hsl(var(--white));\r\n            background-color: hsl(var(--base)); \r\n\r\n        } \r\n    }\r\n}\r\n.nav-menu {\r\n    .nav-item {\r\n        &:hover { \r\n            .dropdown-menu{\r\n                visibility: visible; \r\n                opacity: 1;\r\n                top: 100% !important;\r\n                transform: scaleY(1);\r\n            }\r\n        }\r\n    }\r\n}\r\n}\r\n\r\n.language-select {\r\n    padding: 33px 0;\r\n}\r\n.language-box {\r\n    width:  calc( 100% - 40px) !important;\r\n    padding-left: 10px !important;\r\n    .select {\r\n        color: hsl(var(--white)) !important;\r\n        background: var(--base-gradient);\r\n        border-radius: 5px;\r\n        position: relative;\r\n        padding: 7px 5px;\r\n        &:focus {\r\n            // border-color: hsl(var(--base));\r\n            border: 0;\r\n        }\r\n        option {\r\n            background-color: hsl(var(--dark));\r\n            color: hsl(var(--white));\r\n        }\r\n    }\r\n}\r\n.global {\r\n    &__icon {\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background: var(--base-gradient);\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n    }\r\n}\r\n/* ========================== Desktop Device End ========================= */\r\n\r\n/* ============================== Small Device ======================= */\r\n@include md-screen {\r\n    .body-overlay.show {\r\n        visibility: visible;\r\n        opacity: 1;\r\n    }\r\n    .nav-menu {\r\n        margin-top: 20px;\r\n        .nav-item {\r\n            padding: 0 12px;\r\n            text-align: left;\r\n            display: block;\r\n            position: relative;\r\n            margin: 0;\r\n            &:hover .nav-link .nav-item__icon {\r\n                transform: rotate(0deg) !important;\r\n            }\r\n        }\r\n    }\r\n    .nav-item {\r\n        &:first-child{\r\n            border-bottom: none;\r\n        }\r\n        &:last-child > a{\r\n            border-bottom: 0;\r\n        }\r\n        .nav-link {\r\n            margin-bottom: 8px;\r\n            padding: 10px 10px 10px 0 !important;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin: 0 !important;\r\n            border-bottom: 1px solid hsl(var(--white) / .2);\r\n            color: hsl(var(--white));\r\n            &::before{\r\n                display: none;\r\n            }\r\n            &.show {\r\n                &[aria-expanded=\"true\"] {\r\n                    color: hsl(var(--white)) !important;\r\n                    i{\r\n                        transform: rotate(180deg);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .dropdown-menu {\r\n        border-radius: 3px;\r\n        -webkit-box-shadow: none;\r\n        border-radius: 3px;\r\n        -webkit-box-shadow: none;\r\n        box-shadow: none;\r\n        border-radius: 2px;\r\n        width: 100%;\r\n        margin: 0px !important;\r\n        padding: 0 !important;\r\n        border: 0;\r\n        background-color: inherit;\r\n        overflow: hidden;\r\n        li{\r\n              &:nth-last-child(1){\r\n                border-bottom: none; \r\n            }\r\n            .dropdown-item{\r\n                padding: 10px 0px;\r\n                font-weight: 500;\r\n                @include fs-16; \r\n                color: hsl(var(--white));\r\n                border-bottom: 1px solid hsl(var(--white) / .2);\r\n                margin-left: 20px;\r\n                color: hsl(var(--white));\r\n                &:hover, &:focus {\r\n                    background-color: transparent;\r\n                }\r\n            }\r\n        }\r\n    } \r\n}\r\n\r\n.navbar-toggler {\r\n    &.header-button {\r\n        border-color: transparent;\r\n        color: hsl(var(--white));\r\n        background: transparent !important;\r\n        padding: 0 !important;\r\n        border: 0 !important;\r\n        border-radius: 0 !important; \r\n        transition: .15s ease-in-out;\r\n        width: auto;\r\n        &:focus {\r\n        box-shadow: none !important;\r\n        }\r\n        &[aria-expanded=\"true\"] i::before{\r\n            content: \"\\f00d\";\r\n        }\r\n        i {\r\n            font-size: 1.5625rem; // 25px\r\n        }\r\n    }\r\n}\r\n// Login Registration\r\n.login-registration-list {\r\n    margin-left: 10px;\r\n    @include md-screen {\r\n        padding-right: 0;\r\n        padding-left: 0;\r\n        margin-left: 0;\r\n    }\r\n    &__item {\r\n        color: hsl(var(--white));\r\n        margin-left: 10px;\r\n    }\r\n    &__icon {\r\n        color: hsl(var(--base));\r\n        margin-right: 10px;\r\n        @extend .fs-16; \r\n    }\r\n   &__link {\r\n        color: hsl(var(--white));\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        font-family: var(--heading-font);\r\n        @include msm-screen {\r\n            @include fs-15; \r\n        }\r\n    }\r\n}\r\n\r\n// ================ Top Header End Here ================\r\n/* ================================= Header Css End =========================== */\r\n", "\r\n/* ====================== Breadcumb Css Start ==================== */\r\n.breadcumb {\r\n    position: relative;\r\n    z-index: 1;\r\n    width: 100%;\r\n    \r\n    &__wrapper {\r\n        text-align: left;\r\n    }\r\n    &__title {\r\n        margin-bottom: 10px;\r\n        color: hsl(var(--white)); \r\n    }\r\n    &__list {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        // justify-content: center;\r\n    }\r\n    &__item{\r\n        color: hsl(var(--white)); \r\n        padding-right: 5px;\r\n        font-weight: 400;\r\n        @extend .fs-16; \r\n        &:last-child {\r\n            padding-right: 0;\r\n        }\r\n        &-text {\r\n            color: hsl(var(--white)); ;\r\n        }\r\n    }\r\n    &__link {\r\n        color: hsl(var(--white));  \r\n        font-weight: 500;\r\n        &:hover {\r\n            color: hsl(var(--base)); ;\r\n        }\r\n    }\r\n    &__inner {\r\n       padding-top: 180px;\r\n       padding-bottom: 170px;\r\n        position: relative;\r\n        @include md-screen {\r\n            padding-top: 110px;\r\n            padding-bottom: 100px;\r\n\r\n        }\r\n        @include sm-screen {\r\n            background-image: none;\r\n            padding-top: 115px;\r\n            padding-bottom: 70px;\r\n            background-color: hsl(var(--section-bg));\r\n\r\n        }\r\n    }\r\n    &__bg {\r\n        background: var(--base-gradient);\r\n        width: 100%;\r\n        height: 100%;\r\n        -webkit-mask-image: url(../images/shapes/bd-3.png);\r\n        mask-image: url(../images/shapes/bd-3.png);\r\n        -webkit-mask-repeat: no-repeat;\r\n        mask-repeat: no-repeat;\r\n        -webkit-mask-position: center center;\r\n        mask-position: center center;\r\n        -webkit-mask-size: cover;\r\n        mask-size: cover;\r\n        position: absolute;\r\n        top: 20px;\r\n        left: 0;\r\n        z-index: -1;\r\n        @include sm-screen {\r\n           display: none;\r\n        }\r\n    }\r\n}\r\n/* ====================== Breadcumb Css End ==================== */\r\n", "\r\n/* ============= Footer Start Here ======================= */\r\n.footer-area {\r\n    z-index: 1;\r\n    &-inner {\r\n        position: relative;\r\n        margin-top: 70px;\r\n        padding-top: 100px;\r\n        overflow: hidden;\r\n        @include sm-screen {\r\n            margin-top: 0;\r\n            padding-top: 30px;\r\n            background-image: none !important;\r\n            background-color: hsl(var(--section-bg));\r\n        }\r\n    }\r\n    &__bg {\r\n        background: var(--base-gradient);\r\n        width: 100%;\r\n        height: 100%;\r\n        -webkit-mask-image: url(../images/shapes/footer-shape02.png);\r\n        mask-image: url(../images/shapes/footer-shape02.png);\r\n        -webkit-mask-repeat: no-repeat;\r\n        mask-repeat: no-repeat;\r\n        -webkit-mask-position: center center;\r\n        mask-position: center center;\r\n        -webkit-mask-size: cover;\r\n        mask-size: cover;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 50%;  \r\n        z-index: -1;\r\n        transform: translateX(-50%);\r\n        @include sm-screen {\r\n            display: none;\r\n        }\r\n      \r\n    }\r\n    \r\n}\r\n// @media (max-width:1799px) {\r\n//     .footer-area {\r\n//         &__shape {\r\n//             img {\r\n//                 width: 300px;\r\n//             }\r\n//         }\r\n//     }\r\n// }\r\n.footer-shape {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n}\r\n@media (max-width:1799px) {\r\n    .footer-shape {\r\n        display: none;\r\n    }\r\n}\r\n.footer-item {\r\n    &__logo {\r\n        margin-bottom: 20px;\r\n        a {\r\n            img {\r\n                width: 100%;\r\n                height: 100%;\r\n                max-width: 160px;\r\n                max-height: 64px;\r\n            }\r\n        }\r\n    }\r\n    &__title {\r\n        color: hsl(var(--white));\r\n        padding-bottom: 10px;\r\n        margin-bottom: 25px;\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        font-family: var(--body-font);\r\n\r\n    }\r\n    .social-list {\r\n        margin-top: 30px;\r\n        @include md-screen {\r\n            margin-top: 20px;\r\n        }\r\n        @include msm-screen {\r\n            margin-top: 15px;\r\n        }\r\n    }\r\n}\r\n// Footer List Item\r\n.footer-menu {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin: -5px 0;\r\n    &__item {\r\n        display: block;\r\n        padding: 5px 0;\r\n    }\r\n    &__link {\r\n        font-size: 16px;\r\n        font-family: var(--body-font);\r\n        font-weight: 400;\r\n        color: hsl(var(--white) / .8);\r\n        &:hover {\r\n            color: hsl(var(--base));\r\n            margin-left: 4px;\r\n        }\r\n    }\r\n}\r\n// Footer Contact\r\n.footer-contact-menu {\r\n    margin: -6px 0;\r\n    &__item {\r\n        display: flex;\r\n        padding: 6px 0;\r\n        &-icon {\r\n            width: 15px;\r\n            color: hsl(var(--base-two));\r\n            font-size: 1rem; // 20px\r\n        }\r\n        &-content {\r\n            width: calc(100% - 15px);\r\n            padding-left: 15px;\r\n        }\r\n    }\r\n}\r\n/* ============= Footer End Here ======================= */\r\n\r\n/* ============= Bottom Footer End Here =======================*/\r\n.bottom-footer {\r\n    // background-color: hsl(var(--dark));\r\n    border-top: 1px solid hsl(var(--white) / .1);\r\n    &-text {\r\n        color: hsl(var(--white) / .8);\r\n        font-weight: 400;\r\n        font-family: var(--body-font);\r\n        font-size: 16px;\r\n    }\r\n    &__left {\r\n        @include xsm-screen {\r\n            @include fs-15;\r\n        }\r\n        a {\r\n            color: hsl(var(--base));\r\n            font-weight: 600;\r\n            padding: 0 5px;\r\n           \r\n            &:hover {\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n    }\r\n}\r\n/* =============Bottom Footer End Here ======================= */\r\n\r\n", "/* ================================= preload Css Start =========================== */\r\n.preloader{\r\n  position: fixed;\r\n  z-index: 999999;\r\n  background-color: hsl(var(--black));\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.loader-p{\r\n  border: 0 solid transparent;\r\n  border-radius: 50%;\r\n  width: 150px;\r\n  height: 150px;\r\n  position: absolute;\r\n  top: calc(50vh - 75px);\r\n  left: calc(50vw - 75px);\r\n}\r\n\r\n.loader-p:before, .loader-p:after{\r\n  content: '';\r\n  border: 1em solid hsl(var(--base));\r\n  border-radius: 50%;\r\n  width: inherit;\r\n  height: inherit;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  animation: loader 2s linear infinite;\r\n  opacity: 0;\r\n}\r\n\r\n.loader-p:before{\r\n  animation-delay: 0.5s;\r\n}\r\n\r\n@keyframes loader{\r\n  0%{\r\n    transform: scale(0);\r\n    opacity: 0;\r\n  }\r\n  50%{\r\n    opacity: 1;\r\n  }\r\n  100%{\r\n    transform: scale(1);\r\n    opacity: 0;\r\n  }\r\n}\r\n/* ================================= preload Css End =========================== */\r\n", "\r\n/* ===================== Scroll to Top Start ================================= */\r\n.scroll-top {\r\n    position: fixed;\r\n    right: 30px;\r\n    bottom: 30px;\r\n    color: hsl(var(--white));\r\n    background-color: hsl(var(--base));\r\n    width: 40px;\r\n    height: 40px;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 3px;\r\n   @extend .fs-16;\r\n    z-index: 5;\r\n    transition: .5s;\r\n    cursor: pointer;\r\n    visibility: hidden;\r\n    opacity: 0;\r\n    bottom: -50px;\r\n    animation: scroll_top 5s linear infinite;\r\n    @include sm-screen {\r\n        right: 20px;\r\n        width: 35px;\r\n        height: 35px;\r\n    }\r\n    &:hover {\r\n        color: hsl(var(--white));\r\n        background-color: hsl(var(--base-two-d-100));\r\n    }\r\n    &.show{\r\n        visibility: visible;\r\n        opacity: 1;\r\n        bottom: 30px;\r\n    }\r\n}\r\n@keyframes scroll_top {\r\n    0%, to{\r\n        transform: translateY(0px); \r\n    }\r\n    50%{\r\n        transform: translateY(20px); \r\n    }\r\n}\r\n/* ===================== Scroll to Top End ================================= */\r\n", "/* ================================= Template Selection Css Start =========================== */\r\n::selection {\r\n    color: hsl(var(--white));\r\n    background: hsl(var(--base-d-100));\r\n  }\r\n::-webkit-scrollbar {\r\n    width: 8px;\r\n    height: 8px;\r\n}\r\n::-webkit-scrollbar-button {\r\n    width: 0px;\r\n    height: 0px;\r\n}\r\n::-webkit-scrollbar-thumb {\r\n    background-color: hsl(var(--base-d-100));\r\n    border: 0px solid transparent;\r\n    border-radius: 2px;\r\n}\r\n\r\n/* ================================= Template Selection Css End =========================== */", "\r\n/* ================================= Social Icon Css Start =========================== */\r\n.social-list {\r\n    display: flex;\r\n    flex-wrap: wrap; \r\n    align-items: center;\r\n    &__item {\r\n        margin-right: 10px;\r\n        &:last-child {\r\n            margin-right: 0;\r\n        }\r\n    }\r\n    &__link {\r\n        width: 40px;\r\n        height: 40px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        border-radius: 50%;\r\n        position: relative;\r\n        overflow: hidden;\r\n        background-color: hsl(var(--white) / .1);\r\n        border-radius: 50%;\r\n        transition: .3s;\r\n        cursor: pointer;\r\n        color: hsl(var(--white));\r\n        &.active {\r\n            background: var(--base-gradient);\r\n            color: hsl(var(--white)) !important;\r\n            border-color: hsl(var(--base)) !important;\r\n        }\r\n        @include sm-screen {\r\n            width: 35px;\r\n            height: 35px;\r\n            @include fs-14;\r\n        }\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--base));\r\n            color: hsl(var(--white));\r\n            border-color: hsl(var(--base));\r\n        }\r\n    }\r\n}\r\n/* ================================= Social Icon Css End =========================== */", "\r\n/* ============ Sidebar search box ============= */\r\n.search-box {\r\n    position: relative;\r\n    &__button {\r\n        position: absolute;\r\n        right: 15px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        color: hsl(var(--white)/.2); \r\n    }\r\n}\r\n\r\n/* ================== Sidebar Box & Title =================== */\r\n.blog-sidebar {\r\n    background-color: transparent; \r\n    padding: 0px 20px;\r\n    border-radius: 5px;\r\n    margin-bottom: 30px;\r\n    &:last-of-type {\r\n        margin-bottom: 0;\r\n    }\r\n    @include msm-screen {\r\n        padding: 20px 15px;\r\n    }\r\n    &__title {\r\n        position: relative;\r\n        padding-bottom: 10px;\r\n        color: hsl(var(--white)); \r\n    }\r\n}\r\n\r\n/* ========================= Category & Tags List Style ========================= */\r\n.text-list {\r\n    // Category\r\n    &.style-category {\r\n        .text-list__item {\r\n            border-bottom: 1px solid hsl(var(--white) / .2);\r\n        }\r\n        .text-list__link{\r\n            color: hsl(var(--white)); \r\n            padding: 10px 0;\r\n            &:hover {\r\n                color: hsl(var(--base)); \r\n            }\r\n        }\r\n    }\r\n    // Style Tag\r\n    // &.style-tag {\r\n    //     display: flex;\r\n    //     flex-direction: row;\r\n    //     flex-wrap: wrap;\r\n    //     margin: -7px;\r\n    //     .text-list__item {\r\n    //        padding: 7px;\r\n    //        display: block;\r\n    //     }\r\n    //     .text-list__link{\r\n    //         color: hsl(var(--white)); \r\n    //         border: .5px solid hsl(var(--white) / .09); \r\n    //         padding: 10px 20px;\r\n    //         border-radius: 3px;\r\n    //         font-size: 16px;\r\n    //         display: block;\r\n    //         &.active {\r\n    //             color: hsl(var(--white)); \r\n    //             background-color: hsl(var(--base)); \r\n    //             border-color: hsl(var(--base)); \r\n    //         }\r\n    //         &:hover {\r\n    //             color: hsl(var(--white)); \r\n    //             background-color: hsl(var(--base)); \r\n    //             border-color: hsl(var(--base)); \r\n    //         }\r\n    //     }\r\n\r\n    // }\r\n}\r\n\r\n/* ======================== Latest blog======================== */\r\n\r\n", "/* ================================= Dashboard Fluid Css Start =========================== */\r\n.dashboard-fluid {\r\n    .dashboard {\r\n        position: relative;\r\n        &__inner {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n\r\n        } \r\n        &__right {\r\n            width: calc(100% - 340px);\r\n            @include lg-screen {\r\n                width: 100%;\r\n            }\r\n        }\r\n    }\r\n    /* ======================= Dashboard Header Start======================= */\r\n    .dropdown {\r\n        display: inline-block !important;\r\n        @include sm-screen {\r\n        }\r\n    }\r\n    .dashboard-header {\r\n        padding: 0 60px;\r\n        background: var(--base-gradient);\r\n        @media (max-width: 1699px) {\r\n            padding: 0 20px;\r\n       }\r\n        &__inner {\r\n            padding: 15px 0;\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n        }\r\n        &__left {\r\n            &-title {\r\n                color: hsl(var(--white));\r\n                margin-bottom: 0;\r\n                font-size: 24px;\r\n                font-family: var(--heading-font);\r\n                font-weight: 600;\r\n            }\r\n        }\r\n    }\r\n    // Header Left\r\n  \r\n    // header Right\r\n    .user-info {\r\n        position: relative;\r\n        text-align: center;\r\n        &__content {\r\n            margin-left: 15px;\r\n            text-align: left;\r\n        }\r\n        .user-info-dropdown {\r\n            border-radius: 4px;\r\n            overflow: hidden;\r\n            transition: 0.25s linear;\r\n            background-color: hsl(var(--white));\r\n            width: 200px;\r\n            position: absolute;\r\n            right: 0;\r\n            z-index: 9;\r\n            top: 100%;\r\n            margin-top: 18px;\r\n            transform: scale(.95);\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            &.show {\r\n                visibility: visible;\r\n                opacity: 1;\r\n                transform: scale(1);\r\n            }\r\n            @include md-screen {\r\n                transform: unset !important;\r\n                top: 43px !important;\r\n            }\r\n            &__item{\r\n                &:last-child {\r\n                    .dropdown-item {\r\n                        border-bottom: 0 !important;\r\n                    }\r\n                }\r\n            }\r\n            &__link {\r\n                border-bottom: 1px dashed hsl(var(--dark)/.1) !important;\r\n                padding: 8px 16px !important;\r\n                color: hsl(var(--dark)) !important;\r\n                margin-left: 0 !important;\r\n                width: 100%;\r\n                text-align: left;\r\n                &:active {\r\n                    background-color: hsl(var(--base));\r\n                }\r\n                &:hover {\r\n                    background-color: hsl(var(--base));\r\n                    color: hsl(var(--white)) !important;\r\n                }\r\n            }\r\n        }\r\n        &__link {\r\n            font-family: var(--body-font);\r\n            font-weight: 700;\r\n            color: hsl(var(--white));\r\n        }\r\n        &__info {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            align-items: center;\r\n        }\r\n        &__thumb {\r\n            width: 40px;\r\n            height: 40px;\r\n            overflow: hidden;\r\n            border-radius: 50%;\r\n            cursor: pointer;\r\n            img {\r\n                @extend .fit-image; \r\n            }\r\n        }\r\n\r\n        &__name {\r\n            color: hsl(var(--white));\r\n            @extend .fs-15; \r\n            display: block;\r\n            font-weight: 400;\r\n            font-family: var(--heading-font);\r\n        }\r\n    }\r\n    /* ======================= Dashboard Header End======================= */\r\n    \r\n    /* ========== Dashboard Body Start =============== */\r\n    .dashboard-body {\r\n        position: relative;\r\n        padding: 30px 20px;\r\n        @include lg-screen {\r\n            padding: 25px 15px;\r\n        }\r\n        &__bar {\r\n            &-icon {\r\n                color: hsl(var(--white));\r\n                font-size: 1.5625rem; //25px\r\n                margin-bottom: 10px;\r\n                cursor: pointer;\r\n                width: 50px;\r\n                height: 40px;\r\n                line-height: 40px;\r\n                background-color: hsl(var(--base));\r\n                text-align: center;\r\n                border-radius: 5px;\r\n            }\r\n        }\r\n    }\r\n    // Dashboard Card\r\n    .dashboard-card {\r\n        padding: 25px 30px;\r\n        border-radius: 5px;\r\n        position: relative;\r\n        z-index: 1;\r\n        overflow: hidden;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        background-color: hsl(var(--section-bg));\r\n        @include sm-screen {\r\n            padding: 15px;\r\n        } \r\n        &__shape {\r\n            -webkit-mask-image: url(../images/shapes/dashboard-shape1.png);\r\n            -webkit-mask-size: 100% 100%;\r\n            mask-size: 100% 100%;\r\n            -webkit-mask-repeat: no-repeat;\r\n            mask-repeat: no-repeat;\r\n            background: var(--base-gradient);\r\n            width: 100%;\r\n            height: 100%;\r\n            position: absolute;\r\n            left: 0;\r\n            z-index: -1;\r\n            top: 0;\r\n        }\r\n        &__header {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            margin-bottom: 30px;\r\n            @include msm-screen {\r\n                margin-bottom: 25px;\r\n            }\r\n            &-icon {\r\n                background: var(--dashboard);\r\n                width: 50px;\r\n                height: auto;\r\n                display: flex;\r\n                justify-content: center;\r\n                align-items: center;\r\n                color: hsl(var(--white));\r\n                border-radius: 110% 150% 100% 120%/100% 170% 90% 130%;\r\n                &.style-one {\r\n                    background: var(--dashboard-one);\r\n                }\r\n                &.style-two {\r\n                    background: var(--dashboard-two);\r\n                }\r\n                &.style-three {\r\n                    background: var(--dashboard-three);\r\n                }\r\n                &.style-four {\r\n                    background: var(--dashboard-four);\r\n                }\r\n                &.style-five {\r\n                    background: var(--dashboard-five);\r\n                }\r\n            }\r\n            &-title {\r\n                margin-bottom: 0;\r\n                @extend .fs-16;\r\n                font-weight: 400;\r\n                font-family: var(--body-font);\r\n                color: hsl(var(--white)/.8);\r\n            }\r\n            &-currency {\r\n                font-size: 20px;\r\n                font-weight: 700;\r\n                font-family: var(--heading-font);\r\n                color: hsl(var(--white));\r\n            }\r\n            &-content {\r\n                width: calc( 100% - 50px);\r\n                padding-left: 20px;\r\n            }\r\n        }\r\n        &__item {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            gap: 10px;\r\n            width: 100%;\r\n            @include sm-screen {\r\n                gap: 6px;\r\n            }\r\n        }\r\n        &__content {\r\n          border: 1px solid hsl(var(--white)/.09);\r\n          padding: 15px;\r\n          border-radius: 5px;\r\n          width: calc( 33% - 10px);\r\n          @media (max-width:1699px) {\r\n            padding: 10px;\r\n          }\r\n          @include xxxl-screen {\r\n            padding: 8px;\r\n          }\r\n          @include xxl-screen {\r\n             padding: 6px;\r\n          }\r\n          @include sm-screen {\r\n            padding: 5px;\r\n            width: calc( 33% - 6px);\r\n          }\r\n          @include msm-screen {\r\n            padding: 7px;\r\n          }\r\n        }\r\n        &__text {\r\n            font-weight: 600;\r\n            font-family: var(--body-font);\r\n            font-size: 12px;\r\n            color: hsl(var(--white)/.8);\r\n        } \r\n        &__amount {\r\n            margin-bottom: 0;\r\n            font-size: 16px;\r\n            @include xxxl-screen {\r\n                font-size: 14px;\r\n            }\r\n            @include sm-screen {\r\n                font-size: 13px;\r\n            }\r\n            @include msm-screen {\r\n                font-size: 14px;\r\n            }\r\n        }  \r\n        &__icon {\r\n            font-size: 2.5rem; //40px\r\n            width: 60px;\r\n            height: 60px;\r\n            color: hsl(var(--base));\r\n            background-color: hsl(var(--body-background));\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            border-radius: 50%;\r\n            border: 2px solid hsl(var(--base));\r\n            position: relative;\r\n            margin: 5px;\r\n            animation: swing 1.5s linear infinite;\r\n            @include sm-screen {\r\n                font-size: 1.5625rem; // 25px\r\n                width: 50px;\r\n                height: 50px;\r\n            }\r\n        }\r\n    }\r\n   /* ======================= apexcart css start here ======================= */\r\n    #chart {\r\n        max-width: 400px;\r\n        @include lg-screen {\r\n            max-width: 450px;\r\n        }\r\n        @include sm-screen {\r\n            max-width: 400px;\r\n        }\r\n        @include xsm-screen {\r\n            max-width: 300px;\r\n        }\r\n      }      \r\n      .details{\r\n        background-color: hsl(var(--section-bg));\r\n        border-radius: 5px;\r\n        padding: 20px;\r\n        height: 100%;\r\n        max-width: 800px;\r\n        @include lg-screen {\r\n            max-width: 100%;\r\n        }\r\n        &__list{\r\n          &-item {\r\n            margin-bottom: 10px;\r\n            &-color {\r\n                width: 12px;\r\n                height: 12px;\r\n                border-radius: 50%;\r\n                margin-right: 20px;\r\n                &.one {\r\n                    background-color: hsl(210, 92%, 37%);\r\n                }\r\n                &.two {\r\n                    background-color: hsl(53, 90%, 46%);\r\n                }\r\n                &.three {\r\n                    background-color: hsl(350, 95%, 64%);\r\n                }\r\n                &.four {\r\n                    background-color:hsl(288, 100%, 52%);\r\n                }\r\n                &.five {\r\n                    background-color: hsl(262, 84%, 43%);\r\n                }\r\n                &.six {\r\n                    background-color: hsl(191, 100%, 50%);\r\n                }\r\n                &.seven {\r\n                    background-color: hsl(207, 100%, 47%);\r\n                }\r\n                &.eight {\r\n                    background-color: hsl(181, 96%, 39%);\r\n                }\r\n            }\r\n          }\r\n        }\r\n      }   \r\n      .total-profit {\r\n        background-color: hsl(var(--section-bg));\r\n        padding: 20px;\r\n        border-radius: 5px;\r\n      }\r\n      .invest {\r\n        .select {\r\n            background: var(--base-gradient);\r\n            border-radius: 5px;\r\n            padding: 5px 10px;\r\n            color: hsl(var(--white));\r\n        }\r\n      }\r\n      .time {\r\n        margin-left: 10px;\r\n        .select {\r\n            background: var(--base-gradient);\r\n            border-radius: 5px;\r\n            padding: 5px 10px;\r\n            color: hsl(var(--white));\r\n        }\r\n      }\r\n    //============= apexchart end here ===============\r\n    //========= dashboard profit css start here =========\r\n     .dashboard-item {\r\n        background-color: hsl(var(--section-bg));\r\n        padding: 20px 30px;\r\n        border-radius: 8px;\r\n        height: 100%;\r\n        &__title {\r\n            margin-bottom: 40px;\r\n            @include md-screen {\r\n                margin-bottom: 20px;\r\n            }\r\n        }\r\n        &__investment {\r\n            text-align: center;\r\n        }\r\n        &__wallet {\r\n            background-color: hsl(var(--white)/.05);\r\n            padding: 15px;\r\n            border-radius: 8px;\r\n            width: 50%;\r\n            @include xxsm-screen {\r\n                padding: 10px;\r\n            }\r\n            &-title {\r\n                font-size: 14px;\r\n            }\r\n            &.two {\r\n                margin-left: 10px;\r\n            }\r\n            &-number {\r\n                margin-bottom: 5px;\r\n                @include xxxl-screen {\r\n                    font-size: 16px;\r\n                }\r\n            }\r\n            &-persentage {\r\n                color: hsl(142, 61%, 59%);\r\n            }\r\n        }\r\n        &__pay {\r\n            margin-bottom: 20px;\r\n            &-text {\r\n                font-size: 14px;\r\n            }\r\n            &-number {\r\n                font-size: 18px;\r\n                font-family: var(--heading-font);\r\n                font-weight: 700;\r\n                color: hsl(var(--white));\r\n            }\r\n        }\r\n        &__desc {\r\n            margin-top: 50px;\r\n            font-size: 16px;\r\n            border-top: 1px solid hsl(var(--white)/.1);\r\n            padding-top: 30px;\r\n        }\r\n        .price {\r\n            font-size: 14px;\r\n            font-weight: 500;\r\n        }\r\n     }\r\n    /* ========== dashboard profit css end here ========== */\r\n   /* ==============progress bar css ============== */\r\n    .progress-basic .progress-bar {\r\n        transition: width 0.6s ease;\r\n        height: 5px;\r\n      }\r\n      .progress-basic-1 {\r\n        width: 60%;\r\n        background-color: hsl(207, 100%, 47%);\r\n      }\r\n      .progress-basic-2 {\r\n        width: 80%;\r\n        background-color: hsl(288, 100%, 52%);\r\n      }\r\n      .progress-basic-3 {\r\n        width: 70%;\r\n        background-color: hsl(191, 100%, 50%);\r\n      }\r\n      .progress-basic .progress {\r\n        background-color: #eeedf3;\r\n        border-radius: 10px;\r\n        height: 5px;\r\n      }\r\n      .progress-labels .progress {\r\n        background-color: #eeedf3;\r\n        border-radius: 10px;\r\n        height: 25px;\r\n      }\r\n      .progress-labels .progress-bar {\r\n        background-color:hsl(var(--white));\r\n        height: 25px;\r\n        font-size: 15px;\r\n      }\r\n    .investment-wrapper {\r\n      margin-bottom: 10px;\r\n      @extend .flex-between;\r\n     &__title {\r\n        margin-bottom: 0px;\r\n     }\r\n     &__interest {\r\n        font-size: 12px;\r\n        font-weight: 700;\r\n     }\r\n     &__date {\r\n        font-size: 12px;\r\n     }\r\n     &__icon {\r\n        width: 35px;\r\n        height: 35px;\r\n        background-color: hsl(207, 100%, 47%);\r\n        color: hsl(var(--white));\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 7px;\r\n        &.style-two {\r\n            background-color: hsl(288, 100%, 52%);\r\n        }\r\n        &.style-three {\r\n            background-color: hsl(191, 100%, 50%);\r\n        }\r\n     }\r\n    &__rate {\r\n        width: calc(100% - 35px);\r\n        padding-left: 10px;\r\n    }\r\n}\r\n\r\n // ====================== Dashboard Right End========================     \r\n}\r\n#timeline-chart .apexcharts-toolbar {\r\n    opacity: 1;\r\n    border: 0;\r\n  }\r\n/* ================================= Dashboard Fluid Css End =========================== */\r\n", "/* ================================= Body Overlay Start =========================== */\r\n.body-overlay{\r\n    position: fixed;\r\n    width: 100%;\r\n    height: 100%;\r\n    content: \"\";\r\n    left: 0;\r\n    top: 0;\r\n    background-color: hsl(var(--black) / .6);\r\n    z-index: 99;\r\n    transition: .2s linear;\r\n    visibility: hidden;\r\n    opacity: 0;\r\n    &.show-overlay {\r\n        visibility: visible;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.sidebar-overlay{\r\n    position: fixed;\r\n    width: 100%;\r\n    height: 100%;\r\n    content: \"\";\r\n    left: 0;\r\n    top: 0;\r\n    background-color: hsl(var(--black) / .6);\r\n    z-index: 99;\r\n    transition: .2s linear;\r\n    visibility: hidden;\r\n    opacity: 0;\r\n    &.show {\r\n        visibility: visible;\r\n        opacity: 1;\r\n        z-index: 999;\r\n    }\r\n}\r\n/* ================================= Body Overlay End =========================== */", "/* ============== Search Form Start ================== */\r\n.search-form {\r\n    margin-top: 50px;\r\n    border: 12px solid hsl(var(--white) / .2);\r\n    border-radius: 40px;\r\n    overflow: hidden;\r\n    @include msm-screen {\r\n        border-width: 7px;\r\n    }\r\n    .form--control {\r\n        background-color: hsl(var(--white));\r\n        color: hsl(var(--base-two));\r\n        border-radius: 0;\r\n        font-weight: 500;\r\n        border-radius: 40px;\r\n        padding: 15px;\r\n        padding-right: 150px; \r\n        &::placeholder {\r\n            color: hsl(var(--black) / .4) !important;\r\n            font-weight: 500;\r\n        }\r\n    }\r\n    .btn--base {\r\n        position: absolute;\r\n        right: 5px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        display: flex;\r\n        height: auto;\r\n        align-items: center;\r\n        border-radius: 40px !important;\r\n        padding: 10px 20px;\r\n        @extend .fs-15; \r\n        &:hover, &:focus {\r\n            color: hsl(var(--white)); \r\n            background-color: hsl(var(--base));\r\n        }\r\n    }\r\n}\r\n/* ============== Search Form End ================== */", "/* ================================= Dashboard Fulid Sidebar Css Start =========================== */\r\n.dashboard-fluid {\r\n  // Sidebar logo Css Start\r\n  .sidebar-logo {\r\n    background-color: transparent;\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n    position: relative;\r\n    &__link {\r\n      display: block;  \r\n      width: 100%;\r\n      padding: 50px 20px;\r\n      img {\r\n        max-width: 180px;\r\n        max-height: 100px;\r\n      }\r\n    }\r\n  }\r\n  // Sidebar logo Css End\r\n /* =====balance css start here ===== */\r\n  .balance {\r\n    padding: 0 20px;\r\n    &__title {\r\n      margin-bottom: 10px;\r\n    }\r\n    &__item {\r\n      background-color: hsl(var(--white)/0.09);\r\n      padding: 10px 20px;\r\n      border-radius: 5px;\r\n      margin-bottom: 15px;\r\n      &-wallet {\r\n        @extend .fs-16;\r\n        font-family: var(--body-font);\r\n        font-weight: 400;\r\n        color: hsl(var(--white)/.5);\r\n      }\r\n      &-number {\r\n        margin-bottom: 0;\r\n        font-weight: 700;\r\n        font-family: var(--body-font);\r\n      }\r\n      &-currency {\r\n        @extend .fs-16;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n    &__button {\r\n      margin-top: 30px;\r\n      border-bottom: 1px solid hsl(var(--white)/.1);\r\n      padding-bottom: 60px;\r\n      &-one {\r\n        background: var(--base-gradient);\r\n        padding: 15px 35px;\r\n        border-radius: 5px;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        font-family: var(--body-font);\r\n        &:last-child {\r\n          background-color: hsl(var(--white)/0.09) !important;\r\n          background-image: none;\r\n          margin-left: 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n /* ====== balance css end here ====== */\r\n  // Sidebar Menu Start\r\n  .sidebar-menu {\r\n    padding-bottom: 10px;\r\n    height: 100vh;\r\n    background-color: hsl(var(--body-background));\r\n    overflow-y: auto;\r\n    z-index: 999; \r\n    transition: .2s linear;\r\n    padding-top: 0;\r\n    width: 320px;\r\n    border-radius: 0;\r\n    position: sticky;\r\n    top: 0;\r\n    &::-webkit-scrollbar {\r\n      width: 3px;\r\n      height: 3px;\r\n  }\r\n    /*=========== Sidebar Show hide Css Start ========= */\r\n    &.show-sidebar {\r\n        transform: translateX(0);\r\n    }\r\n    @include lg-screen {\r\n        position: fixed;\r\n        left: 0;\r\n        top: 0;\r\n        min-width: 320px;\r\n        transform: translateX(-100%);\r\n        z-index: 9992;\r\n        border-radius: 0;\r\n        border-right: 1px solid hsl(var(--white)/0.1);\r\n    }\r\n    &__close {\r\n        position: absolute;\r\n        top: 12px;\r\n        right: 12px;\r\n        color: hsl(var(--white));\r\n        border: 1px solid hsl(var(--white)/.7);\r\n        width: 35px;\r\n        height: 35px;\r\n        line-height: 35px;\r\n        text-align: center;\r\n        border-radius: 3px;\r\n        @extend .fs-18; \r\n        transition: .2s linear;\r\n        cursor: pointer;\r\n        z-index: 9;\r\n        &:hover, &:focus {\r\n            background-color: hsl(var(--white));\r\n            border-color: hsl(var(--white));\r\n            color: hsl(var(--base));\r\n        }\r\n\r\n    }\r\n  /* ========== Sidebar Show hide Css End =========*/\r\n    &-list {\r\n      padding: 0 20px;\r\n        &__item {\r\n          &.active {\r\n            color: hsl(var(--white));\r\n            background-image: var(--base-gradient);\r\n            border-radius: 5px;\r\n          }\r\n          &.has-dropdown {\r\n            &.active {\r\n                > a {\r\n                  color: hsl(var(--dark));\r\n                  background-color: hsl(var(--white));\r\n                  border-radius: 6px 6px 0 0;\r\n                    &:after {\r\n                        transform: rotate(90deg);\r\n                        right: 2px;\r\n                        color: hsl(var(--dark));\r\n                    }\r\n                }\r\n            }\r\n            > a {\r\n                &:after {\r\n                    font-family: \"Line Awesome Free\";\r\n                    font-weight: 900;\r\n                    content: \"\\f105\";\r\n                    font-style: normal;\r\n                    display: inline-block;\r\n                    font-style: normal;\r\n                    font-variant: normal;\r\n                    text-rendering: auto;\r\n                    text-align: center;\r\n                    background: 0 0;\r\n                    position: absolute;\r\n                    right: 0px;\r\n                    top: 14px;\r\n                    transition: .1s linear;\r\n                    color: hsl(var(--white)/.6);\r\n                }\r\n            }\r\n          }\r\n        }\r\n        &__link {\r\n            display: inline-block;\r\n            text-decoration: none;\r\n            position: relative;\r\n            padding: 15px 10px;\r\n            width: 100%;\r\n            color: hsl(var(--white)/.8);\r\n            font-weight: 700;\r\n            @extend .fs-16; \r\n            &.active {\r\n              color: hsl(var(--base));\r\n            }\r\n            .icon{\r\n                margin-right: 5px;\r\n                @extend .fs-15; \r\n                text-align: center;\r\n                border-radius: 4px;\r\n                animation: swing ease-in-out 0.5s 1 alternate;\r\n            }\r\n        }\r\n    }\r\n  }\r\n  // Sidebar Menu End\r\n  // Sidebar Submenu Start\r\n  .sidebar-submenu {\r\n    display: none;\r\n    &.open-submenu {\r\n      display: block;\r\n    }\r\n    &-list {\r\n        padding: 5px 0;\r\n        &__item {\r\n          &.active {\r\n            > a {\r\n              color: hsl(var(--base));\r\n              background-color: hsl(var(--dark));\r\n            }\r\n          }\r\n        }\r\n        &__link {\r\n          padding: 8px 0px;\r\n          display: block;\r\n          color: hsl(var(--dark));\r\n          color: hsl(var(--white)/0.9);\r\n          font-weight: 400;\r\n          @extend .fs-15; \r\n          margin-left: 20px;\r\n            &:before {\r\n                content: \"\\f111\";\r\n                font-family: \"Font Awesome 5 Free\";\r\n                font-weight: 400;\r\n                font-style: normal;\r\n                display: inline-block;\r\n                text-align: center;\r\n                text-decoration: none;\r\n                margin-right: 10px;\r\n                font-size: 0.5rem; //8px\r\n            }\r\n        }\r\n    }\r\n  }\r\n  /* Sidebar Submenu End */\r\n}\r\n/* ================================= Dashboard Fulid Sidebar Css End =========================== */", "/* =========================== Banner Section Start Here =========================*/\r\n.banner-section {\r\n    position: relative;\r\n    background: var(--banner-gradient);\r\n    z-index: 1;\r\n    overflow: hidden;\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n    @include lg-screen {\r\n        padding-top: 50px;\r\n    }\r\n    @include md-screen {\r\n        padding-top:25px;\r\n    }\r\n    @include sm-screen  {\r\n        padding-top: 180px;\r\n    }\r\n    @include msm-screen {\r\n        padding-top: 160px;\r\n    }\r\n    &.bg-img {\r\n        @include md-screen {\r\n            background-position: center right;\r\n            object-fit: contain;\r\n        }\r\n    }\r\n\r\n}\r\n.planet-bg {\r\n    position: absolute;\r\n    left: 190px;\r\n    top: 220px;\r\n    z-index: -1;\r\n}\r\n.planet-small {\r\n    position: absolute;\r\n    right: 40px;\r\n    top: 280px;\r\n    z-index: -1;\r\n}\r\n.banner-content {\r\n    position: relative;\r\n    z-index: 9;\r\n    @include sm-screen {\r\n        text-align: center;\r\n    }\r\n    &__title {\r\n        font-size: 50px;\r\n        margin-bottom: 0;\r\n        @include md-screen {\r\n            font-size: 45px;\r\n        }\r\n        @include sm-screen {\r\n            font-size: 35px;\r\n        }\r\n        @include msm-screen {\r\n            font-size: 30px;\r\n        }\r\n    }\r\n    &__desc {\r\n        max-width: 600px;\r\n        color: hsl(var(--white) / .8);\r\n    }\r\n    &__buttons {\r\n        padding-top: 50px;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n        @include md-screen {\r\n            padding-top: 30px;\r\n        }\r\n        @include sm-screen {\r\n            justify-content: center;\r\n            align-items: center;\r\n        }\r\n    }\r\n}\r\n/* ====city scene css start here==== */\r\n\r\n.city-scene {\r\n    position: relative;\r\n    width: 856px;\r\n    height: 800px;\r\n    z-index: 1;\r\n    @include lg-screen {\r\n        height: 600px;\r\n    }\r\n    @include md-screen {\r\n        height: 475px;\r\n    }\r\n    @include sm-screen {\r\n        height: 195px;\r\n        opacity: 0.5;\r\n    }\r\n    @include msm-screen {\r\n        height: 130px;\r\n    }\r\n}\r\n.bd-1 {\r\n    position: absolute;\r\n    left: 90px;\r\n    bottom: 0px;\r\n    z-index: 40;\r\n    animation: building-move 1.7s ease;\r\n}\r\n.bd-2 {\r\n    position: absolute;\r\n    left: 140px;\r\n    bottom: 0px;\r\n    z-index: 41;\r\n    animation: building-move 1.6s ease;\r\n}\r\n.bd-3 {\r\n    position: absolute;\r\n    left: 370px;\r\n    bottom: 0px;\r\n    z-index: 43;\r\n    animation: building-move 1.8s ease;\r\n}\r\n.bd-4{\r\n    position: absolute;\r\n    left: 467px;\r\n    bottom: 0px;\r\n    z-index: 44;\r\n    animation: building-move 1.5s ease;\r\n}\r\n.bd-5 {\r\n    position: absolute;\r\n    right: 250px;\r\n    bottom: 0px;\r\n    z-index: 50;\r\n    animation: building-move 1.8s ease;\r\n}\r\n.bd-6 {\r\n    position: absolute;\r\n    right: 150px;\r\n    bottom: 0px;\r\n    z-index: 51;\r\n    animation: building-move 1.6s ease;\r\n}\r\n.bd-7 {\r\n    position: absolute;\r\n    right: 20px;\r\n    bottom: 0px;\r\n    z-index: 52;\r\n    animation: building-move 1.5s ease;\r\n}\r\n.bd-8 {\r\n    position: absolute;\r\n    left: 135px;\r\n    bottom: 102px;\r\n    z-index: 30;\r\n    animation: building-move 1.8s ease;\r\n}\r\n.bd-9 {\r\n    position: absolute;\r\n    left: 424px;\r\n    bottom: 0px;\r\n    z-index: 42;\r\n    animation: building-move 1.85s ease;\r\n}\r\n.bd-10 {\r\n    position: absolute;\r\n    right: 110px;\r\n    bottom: 85px;\r\n    z-index: 30;\r\n    animation: building-move 1.5s ease;\r\n}\r\n.bd-11 {\r\n    position: absolute;\r\n    right: 215px;\r\n    bottom: 95px;\r\n    z-index: 29;\r\n    animation: building-move 2.1s ease;\r\n}\r\n\r\n.bd-12 {\r\n    position: absolute;\r\n    left: 180px;\r\n    bottom: 140px;\r\n    z-index: 20;\r\n    animation: building-move 2.3s ease;\r\n}\r\n.bd-13 {\r\n    position: absolute;\r\n    right: 95px;\r\n    bottom: 180px;\r\n    z-index: 19;\r\n    animation: building-move 2.4s ease;\r\n}\r\n.bd-14 {\r\n    position: absolute;\r\n    right: 80px;\r\n    bottom: 100px;\r\n    z-index: 18;\r\n    animation: building-move 2.6s ease;\r\n}\r\n.bd-15 {\r\n    position: absolute;\r\n    right: 175px;\r\n    bottom: 255px;\r\n    z-index: 10;\r\n    animation: building-move 2.55s ease;\r\n}\r\n.bd-16 {\r\n    position: absolute;\r\n    left: 485px;\r\n    bottom: 260px;\r\n    z-index: 18;\r\n    animation: building-move 4s ease;\r\n}\r\n.bd-17 {\r\n    position: absolute;\r\n    right: 300px;\r\n    bottom: 160px;\r\n    z-index: 9;\r\n    animation: building-move 3.1s ease;\r\n}\r\n.bd-18 {\r\n    position: absolute;\r\n    right: 358px;\r\n    bottom: 200px;\r\n    z-index: 8;\r\n    animation: building-move 3.15s ease;\r\n}\r\n.bd-19 {\r\n    position: absolute;\r\n    right: 485px;\r\n    bottom: 185px;\r\n    z-index: 7;\r\n    animation: building-move 3.3s ease;\r\n}\r\n.bd-20 {\r\n    position: absolute;\r\n    left: 395px;\r\n    bottom: 285px;\r\n    z-index: 7;\r\n    animation: building-move 3.2s ease;\r\n}\r\n.bd-21 {\r\n    position: absolute;\r\n    left: 290px;\r\n    bottom: 255px;\r\n    z-index: 6;\r\n    animation: building-move 3.1s ease;\r\n}\r\n.bd-22 {\r\n    position: absolute;\r\n    left: 140px;\r\n    bottom: 140px;\r\n    z-index: 1;\r\n    animation: building-move 3.6s ease;\r\n}\r\n\r\n/* =====city scene end here======== */\r\n/* =========================== Banner Section End Here ========================= */\r\n/* ===========================train section start here=========================== */\r\n.train-section {\r\n    width: 100%;\r\n    height: 75px;\r\n    position: relative;\r\n    z-index: 555;\r\n    margin-top: -70px;\r\n    overflow: hidden;\r\n}\r\n.train-wrapper {\r\n    position: absolute;\r\n    bottom: 0px;\r\n    right: 0px;\r\n    z-index: 100;\r\n    transform: translateX(1200px);\r\n    animation: train-move 12s linear infinite;\r\n    animation-delay: 1s;\r\n}\r\n.train{\r\n    position: relative;\r\n    &::before {\r\n        content: '';\r\n        position: absolute;\r\n        width: 160px;\r\n        height: 28px;\r\n        bottom: -2px;\r\n        left: -158px;\r\n        background: url(../../assets//images/shapes/train-light.png) no-repeat center;\r\n        background-size: 160px;\r\n        animation: 0.5s train-light ease-in-out infinite; \r\n    }\r\n}\r\n.railway {\r\n    width: 100%;\r\n    height: 5px;\r\n    background: var(--railway-gradient);\r\n    position: absolute;\r\n    left: 0px;\r\n    bottom: 0px;\r\n}\r\n@keyframes building-move {\r\n    0% {\r\n      transform: translateY(740px);\r\n    }\r\n  \r\n    100% {\r\n      transform: none;\r\n    }\r\n  }\r\n\r\n  @keyframes train-move {\r\n    0% {\r\n      transform: translateX(1200px);\r\n    }\r\n  \r\n    100% {\r\n      transform: translateX(-5000px);\r\n    }\r\n  }\r\n/* ===========================train section end here=========================== */", "/* ============================== About Section Css Start ===================*/\r\n.about-section {\r\n    overflow: hidden;\r\n}\r\n.about-thumb {\r\n    // height: 100%;\r\n    img {\r\n       @extend .fit-image;\r\n    }\r\n}\r\n.about-content{\r\n    &__desc {\r\n        font-size: 20px;\r\n        color: hsl(var(--white)/.8);\r\n        margin-bottom: 30px;\r\n        @include msm-screen {\r\n            font-size: 18px;\r\n        }\r\n        @include xsm-screen {\r\n            font-size: 16px;\r\n        }\r\n    }\r\n    &__item {\r\n        @extend .fs-18;\r\n        display: flex;\r\n        color: hsl(var(--white)/.8);\r\n        margin-bottom: 15px;\r\n        font-weight: 400;\r\n        justify-content: center;\r\n    }\r\n    &__icon {\r\n        padding-right: 26px;\r\n        font-weight: 400;\r\n        color: hsl(var(--white));\r\n        line-height: 2;\r\n        // background: var(--base-gradient);\r\n        // -webkit-background-clip: text;\r\n        // -webkit-text-fill-color: transparent;\r\n    }\r\n}\r\n\r\n/* ============================== About Section Css End ===================*/", "/* ================================ Testimonails Section Css Start ============================= */\r\n.testimonials {\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.testimonails-card {\r\n    padding: 0 10px;\r\n    height: 100%; \r\n}\r\n\r\n.testimonial-item {\r\n    background-color: hsl(var(--white));\r\n    border: 1px solid hsl(var(--black) / .08);\r\n    padding: 30px 20px;\r\n    border-radius: 5px;\r\n    position: relative;\r\n    height: 100%; \r\n    @include xsm-screen {\r\n        padding: 25px 15px;\r\n    }\r\n    &__quate {\r\n        position: absolute;\r\n        right: 20px;\r\n        bottom: 20px;\r\n        width: 80px;\r\n        opacity: 0.08;\r\n    }\r\n    &__content {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n    }\r\n    &__info {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n    }\r\n    &__thumb {\r\n        width: 60px;\r\n        height: 60px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        @include xsm-screen {\r\n            width: 45px;\r\n            height: 45px;\r\n        }\r\n        img {\r\n            @extend .fit-image; \r\n        }\r\n    }\r\n    &__details {\r\n        width: calc(100% - 60px);\r\n        padding-left: 20px;\r\n        @include xsm-screen {\r\n            width: calc(100% - 45px);\r\n            padding-left: 10px;\r\n        }\r\n    }\r\n    &__name {\r\n        margin-bottom: 0;\r\n        color: hsl(var(--black) );\r\n    }\r\n    &__designation {\r\n        color: hsl(var(--black) / .8);\r\n    }\r\n    &__desc {\r\n        margin-top: 20px;\r\n        padding-top: 20px;\r\n        color: hsl(var(--black) / .5);\r\n        border-top: 1px solid hsl(var(--black) / .08);\r\n        @include xsm-screen {\r\n            margin-top: 10px;\r\n            padding-top: 10px;\r\n        }\r\n    }\r\n}\r\n/* ================================ Testimonails Section Css End ============================= */", "/* ================================= Blog Section Css Start Here ================================= */\r\n.blog-item {\r\n    box-shadow: var(--box-shadow);\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    height: 100%;\r\n    &:hover {\r\n        .blog-item {\r\n            &__thumb {\r\n                img {\r\n                    transform: scale(1.2);\r\n                }\r\n            }\r\n            &__title {\r\n                &-link {\r\n                    color: hsl(var(--base));\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &__thumb {\r\n        overflow: hidden;\r\n        border-radius: 8px;\r\n        max-height: 300px;\r\n        &-link {\r\n            width: 100%;\r\n            height: 100%;\r\n            img {\r\n                @extend .fit-image; \r\n                transition: .3s linear;\r\n            }\r\n        }\r\n    }\r\n    &__content {\r\n        background-color: transparent;\r\n        padding: 35px 0px;\r\n        height: 100%;\r\n        @include sm-screen {\r\n            padding: 30px 5px;\r\n        }\r\n        @include xsm-screen {\r\n            padding: 25px 0px;\r\n        }\r\n    }\r\n    &__title {\r\n        margin-top: 10px;\r\n        &-link {\r\n            overflow:hidden;\r\n            text-overflow:ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n        }\r\n    }\r\n    &__desc {\r\n        margin-bottom: 25px;\r\n        overflow:hidden;\r\n        text-overflow:ellipsis;\r\n        display: -webkit-box;\r\n        -webkit-line-clamp: 3;\r\n        -webkit-box-orient: vertical;\r\n        @include sm-screen {\r\n            margin-bottom: 15px;\r\n        }\r\n    }\r\n}\r\n.content-list {\r\n    margin: 0 -20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    @include sm-screen {\r\n        margin: 0 -15px;\r\n    }\r\n    &::before {\r\n        position: absolute;\r\n        content: '';\r\n        top: 50%;\r\n        left: 48%;\r\n        transform: translateY(-50%);\r\n        width: 2px;\r\n        height: 15px;\r\n        background-color: hsl(var(--white)/.2);\r\n    }\r\n    &__item {\r\n        display: inline-block;\r\n        padding: 0 20px;\r\n        color: hsl(var(--white)/.5);\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        font-family: var(--body-font);\r\n        letter-spacing: 1px;\r\n        @include sm-screen {\r\n            padding: 0 14px;\r\n            font-size: 13px;\r\n        }\r\n        @include msm-screen {\r\n            font-size: 14px;\r\n        }\r\n    }\r\n}\r\n/* ================================= Blog Section Css End Here ================================= */", "/*================== investment css start here================== */\r\n.investment-section{\r\n    position: relative;\r\n    background-repeat: inherit !important;\r\n    background-size: unset !important;\r\n    overflow: hidden;\r\n}\r\n.shape-one {\r\n  position: absolute;\r\n  content: \"\";\r\n  top: -170px;\r\n  left: 100px;\r\n  z-index: 1;\r\n  animation: horizontal-move 3s ease-in-out infinite;\r\n  animation-delay: 1s;\r\n  @include md-screen {\r\n    display: none;\r\n  }\r\n}\r\n.shape-two {\r\n    position: absolute;\r\n    content: \"\";\r\n    top: -350px;\r\n    right: 100px;\r\n    z-index: 1;\r\n    animation: horizontal-move 4s ease-in-out infinite;\r\n    animation-delay: 1s;\r\n    @include md-screen {\r\n        display: none;\r\n    }\r\n  }\r\n  @keyframes horizontal-move {\r\n    0% {\r\n      transform: rotateZ(5deg);\r\n    }\r\n  \r\n    50% {\r\n      transform: rotateZ(-5deg);\r\n    }\r\n  \r\n    100% {\r\n      transform: rotateZ(5deg);\r\n    }\r\n  }\r\n/*  ============== plan card css ==================*/\r\n.investment-inner {\r\n    @extend .flex-between;\r\n    @include md-screen {\r\n        display: block;\r\n    }\r\n}\r\n.plan-card-bg {\r\n    background-position: center;\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    padding: 35px;\r\n    border-radius: 10px;\r\n    width: 50%;\r\n    z-index: 2;\r\n    @include md-screen {\r\n        width: 100%;\r\n    }\r\n    @include msm-screen {\r\n        padding: 25px;\r\n    }\r\n    @include xsm-screen {\r\n        padding: 20px;\r\n    }\r\n}\r\n.title {\r\n    margin-bottom: 0;\r\n }\r\n .subtitle {\r\n    font-weight: 500;\r\n }\r\n.plan-list{\r\n    &__item {\r\n        @extend .flex-between;\r\n        @extend .fs-18;\r\n        padding: 10px 15px;\r\n        color: hsl(var(--white));\r\n        background-color: hsl(var(--white)/.1);\r\n        border-radius: 3px;\r\n        margin-top: 5px;\r\n        font-family: var(--heading-font);\r\n        font-weight: 500;\r\n    }\r\n}\r\n.plan-card-select {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: end;\r\n    margin-bottom: -30px;\r\n    .title {\r\n       font-size: 32px;\r\n       @include sm-screen {\r\n        font-size: 24px;\r\n       }\r\n    }\r\n    .select {\r\n        color: hsl(var(--white));\r\n        background-color: hsl(var(--white)/.2) !important;\r\n        @extend .fs-14; \r\n        font-weight: 400;\r\n        border-radius: 30px;\r\n        height: 25px;\r\n        border: none;  \r\n        padding: 0 10px;    \r\n        option {\r\n            background-color: hsl(var(--dark));\r\n        }\r\n    }\r\n}\r\n/* =========================plan card end here======================= */\r\n\r\n/* ===============calculator start here=============== */\r\n\r\n.calculator {\r\n    border-top-right-radius: 10px;\r\n    border-bottom-right-radius: 10px;\r\n    margin-top: 5px;\r\n    padding: 30px 35px;\r\n    width: 50%;\r\n    z-index: 50;\r\n    background-position: center;\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    @include lg-screen {\r\n        padding: 20px 30px;\r\n    }\r\n    @include md-screen {\r\n        width: 100%;\r\n        border-radius: 5px;\r\n    }\r\n    @include xsm-screen {\r\n        padding: 15px 20px ;\r\n    }\r\n    &__title{\r\n        text-align: center;\r\n        font-weight: 700;\r\n    }\r\n}\r\n.position-relative {\r\n    .form--control {\r\n        @include xxsm-screen {\r\n           padding: 5px;\r\n        }\r\n    }\r\n}\r\n.cal-area {\r\n    position: relative;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    &__icon {\r\n    position: absolute;\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 14px;\r\n    text-align: center;\r\n    border-radius: 7px;\r\n    transform: translateY(-50%);\r\n    top: 50%;\r\n    right: 5px;\r\n    background-color: hsl(var(--white)/.09);\r\n    line-height: 35px;\r\n    @include lg-screen {\r\n        width: 30px;\r\n        height: 30px;\r\n        right: 3px;\r\n    }\r\n    @include xsm-screen {\r\n        width: 25px;\r\n        height: 25px;\r\n        line-height: 25px;\r\n        font-size: 12px;\r\n        right: 2px;\r\n    }\r\n    }\r\n}\r\n.input-wrap {\r\n    position: relative;\r\n    width: 45%;\r\n    label {\r\n        margin-bottom: 15px;\r\n        font-size: 16px;\r\n        font-family: var(--heading-font);\r\n        font-weight: 500;\r\n        color: hsl(var(--white));\r\n    }\r\n    .user-wallets {\r\n        margin-bottom: 15px;\r\n        color: hsl(var(--white));\r\n        background-color:transparent;\r\n        padding: 0;\r\n        @extend .fs-14; \r\n        font-weight: 400;\r\n        border-color: none;\r\n        border: none;\r\n        option {\r\n            background-color: hsl(var(--dark));\r\n            color: hsl(var(--white));\r\n        }\r\n    }\r\n}\r\n.profit-result {\r\n    background: var(--profit-gradient);\r\n    border-radius: 10px;\r\n    padding: 10px;\r\n    text-align: center;\r\n    width: 50%;\r\n    border: 1px solid hsl(var(--white)/.07);\r\n}\r\n.profit-value {\r\n    margin-bottom: 0;\r\n}\r\n.cal-bottom-area {\r\n    position: relative;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-around;\r\n    margin-top: 10px;\r\n}\r\n.profit-cal {\r\n    width: 50%;\r\n    &__button {\r\n        margin-top: 20px;\r\n    }\r\n}\r\n/* ===============calculator end here=============== */", "/* ==============plan-section css ============= */\r\n.plan-item {\r\n    text-align: center;\r\n    border-radius: 10px;\r\n    position: relative;\r\n    background-color: hsl(var(--section-bg));\r\n    height: 100%;\r\n    transition: all ease .2s;\r\n    overflow: hidden;\r\n    &:hover {\r\n        transform: scale(1.03);\r\n    }\r\n    &__body {\r\n        padding: 20px 20px 50px 20px;\r\n       \r\n        @include xl-screen {\r\n            padding: 20px 10px 50px 10px;\r\n        }\r\n        @include lg-screen {\r\n            padding: 20px 12px 50px 12px;\r\n        }\r\n        @include sm-screen {\r\n            padding: 20px 15px 30px 15px;\r\n        }\r\n        @include msm-screen {\r\n            padding: 20px 10px 30px 10px;\r\n        }\r\n        @include xsm-screen {\r\n            padding: 20px 20px 30px 20px;\r\n        }\r\n    }\r\n    &__header {\r\n        padding: 27px;\r\n        border-radius: 10px;\r\n        -webkit-mask-image: url(../images/shapes/plan-shape.png);\r\n        -webkit-mask-size: 100% 100%;\r\n        mask-size: 100% 100%;\r\n        -webkit-mask-repeat: no-repeat;\r\n        mask-repeat: no-repeat;\r\n        background: var(--background-gradient);\r\n        width: 100%;\r\n        @include msm-screen {\r\n            padding: 20px;\r\n        }\r\n    }\r\n    &__title {\r\n        margin-bottom: 0;\r\n        font-size: 58px;\r\n        font-family: var(--heading-font);\r\n        font-weight: 700;\r\n        color: hsl(var(--white));\r\n        @include md-screen {\r\n            font-size: 45px;\r\n        }\r\n        @include msm-screen {\r\n            font-size: 30px;\r\n        }\r\n    }\r\n    &__name {\r\n        margin-bottom: 0;\r\n        font-weight: 600;\r\n        letter-spacing: 1px;\r\n    }\r\n    &__info {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        position: relative;\r\n        margin-bottom: 30px;\r\n        &::before {\r\n            position: absolute;\r\n            content: '';\r\n            top: 50%;\r\n            left: 50%;\r\n            transform: translate(-50%, -50%);\r\n            width: .5px;\r\n            height: 25px;\r\n            background-color: hsl(var(--white)/.2);\r\n        }\r\n    }\r\n    &__time {\r\n        margin-bottom: 0;\r\n        font-weight: 600;\r\n        @include lg-screen {\r\n            font-size: 18px;\r\n        }\r\n        @include md-screen {\r\n            font-size: 20px;\r\n        }\r\n        @include sm-screen {\r\n            font-size: 17px;\r\n        }\r\n        @include msm-screen {\r\n            font-size: 16px;\r\n        }\r\n        @include xsm-screen {\r\n            font-size: 20px;\r\n        }\r\n    }\r\n    &__amount {\r\n        font-size: 16px;\r\n        color: hsl(var(--white));\r\n        font-weight: 400;\r\n        @include lg-screen {\r\n            font-size: 14px;\r\n        }\r\n        @include md-screen {\r\n            font-size: 15px;\r\n        }\r\n        @include sm-screen {\r\n            font-size: 14px;\r\n        }\r\n        @include msm-screen {\r\n            font-size: 13px;\r\n        }\r\n        @include xsm-screen {\r\n            font-size: 15px;\r\n        }\r\n    }\r\n    &__list {\r\n        margin-bottom:30px;\r\n        padding-top: 10px;\r\n        border-top: .5px solid hsl(var(--base)/.6);\r\n        @include sm-screen {\r\n            margin-bottom: 20px;\r\n        }\r\n        &-inner {\r\n            @extend .fs-16;\r\n            font-weight: 400;\r\n            font-family: var(--body-font);\r\n            color: hsl(var(--white)/.8);\r\n            padding: 15px 0;\r\n            border-bottom: .5px solid hsl(var(--white)/.1);\r\n            @include sm-screen {\r\n                padding: 10px 0;\r\n            }\r\n            &:last-child {\r\n                border-bottom: 0;\r\n                padding-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n/* =======================plan section css end here======================= */", "/*============================ program css start here ============================*/\r\n.program-section {\r\n  position: relative;\r\n  z-index: 1;\r\n  &__shape {\r\n    position: absolute;\r\n    bottom: 10px;\r\n    left: 200px;\r\n    z-index: -1;\r\n  }\r\n}\r\n/*===== program item css===== */\r\n.program-item {\r\n    padding: 30px;\r\n    background-color: hsl(var(--section-bg));\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    border-radius: 10px;\r\n    margin-top: 20px;\r\n    @include md-screen {\r\n        padding: 25px;\r\n    }\r\n    @include msm-screen {\r\n        padding: 15px;\r\n    }\r\n    &__title {\r\n        margin-bottom: 5px;\r\n        font-weight: 700;\r\n    }\r\n    &__desc {\r\n        max-width: 500px;\r\n        @extend .fs-16;\r\n        color: hsl(var(--white)/.8);\r\n    }\r\n    &__icon {\r\n        @extend .fs-17;\r\n        color: hsl(var(--white));\r\n        background: var(--background-gradient);\r\n        width: 50px;\r\n        height: 50px;\r\n        line-height: 50px;\r\n        text-align: center;  \r\n        border-radius: 50%;\r\n        @include msm-screen {\r\n           width: 40px;\r\n           height: 40px;\r\n           line-height: 40px;\r\n        }\r\n        i {\r\n            line-height: 0;\r\n        }\r\n    }\r\n    &__content {\r\n        width: calc( 100% - 50px );\r\n        padding-left: 20px;\r\n    }\r\n}\r\n.program-thumb {\r\n    img {\r\n        width: 100%;\r\n        height: 100%;\r\n        @include md-screen {\r\n            min-height: 400px;\r\n        }\r\n        @include msm-screen {\r\n            min-height: 300px;\r\n        }\r\n    }\r\n}\r\n/*=========================== program section css end here ===========================*/", "/*============================ process section css start here ============================*/\r\n.process {\r\n    position: relative;\r\n    z-index: 1;\r\n @extend .flex-between;\r\n gap: 24px;\r\n @include md-screen {\r\n    justify-content: center;\r\n    gap: 24px;\r\n }\r\n @include xxsm-screen {\r\n    gap: 12px;\r\n }\r\n}\r\n.process-item {\r\n    padding: 40px 20px;\r\n    border-radius: 10px;\r\n    -webkit-mask-image: url(../images/shapes/process1.png);\r\n    -webkit-mask-size: 100% 100%;\r\n    mask-size: 100% 100%;\r\n    -webkit-mask-repeat: no-repeat;\r\n    mask-repeat: no-repeat;\r\n    background-color: hsl(var(--section-bg)); \r\n    width: calc( 20% - 24px);\r\n    gap: 24px;\r\n    @include md-screen {\r\n        width: calc( 33% - 24px);\r\n        padding: 35px 18px;\r\n    }\r\n    @include sm-screen {\r\n        width: calc( 33% - 24px);\r\n        padding: 30px 15px;\r\n    }\r\n    @include msm-screen {\r\n        width: calc(50% - 24px);\r\n        padding: 20px 10px;\r\n    }\r\n    @include xsm-screen {\r\n        padding: 15px 10px;\r\n    }\r\n    @include xxsm-screen {\r\n        width: calc( 100% - 12px);\r\n        padding: 20px 10px;\r\n    }\r\n    &:nth-child(even) {\r\n        -webkit-mask-image: url(../images/shapes/process2.png);\r\n    }\r\n    &__thumb {\r\n        text-align: center;\r\n        margin: 15px 0;\r\n        @include msm-screen {\r\n            margin: 10px 0;\r\n        }\r\n    }\r\n    &__title {\r\n        text-align: center;\r\n        font-weight: 700;\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n/*============================= process section css end here============================= */", "/* =======================faq-section css start here======================= */\r\n.faq-section {\r\n    position: relative;\r\n    z-index: 1;\r\n    &__shape-one {\r\n        position: absolute;\r\n        left: 0;\r\n        top: 100px;\r\n        z-index: -1;\r\n    }\r\n    &__shape-two {\r\n        position: absolute;\r\n        right: 115px;\r\n        top: 150px;\r\n        z-index: -1;\r\n    }\r\n}\r\n/* ========================faq section css end here========================*/", "/* ==========================investor css start here ========================== */\r\n.investor-item {\r\n    position: relative;\r\n    z-index: 1;\r\n    background-color: hsl(var(--section-bg));\r\n    padding: 40px 60px;\r\n    text-align: center;\r\n    border-radius: 7px;\r\n    transition: .2s linear;\r\n    @include xl-screen {\r\n        padding: 35px 40px;\r\n    }\r\n    @include lg-screen {\r\n        padding: 30px;\r\n    }\r\n    @include msm-screen {\r\n        padding: 25px;\r\n    }\r\n    &:hover {\r\n        transform: translateY(-5px);\r\n        .investor-item{\r\n            &__number {\r\n                color: hsl(var(--white)/.5) !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    &__number {\r\n        color: hsl(var(--white)/.05);\r\n        position: absolute;\r\n        font-size: 58px;\r\n        font-family: var(--heading-font);\r\n        font-weight: 700;\r\n        top: -10px;\r\n        left: 10px;\r\n        z-index: -1;\r\n        transition: .3s linear;\r\n        @include xl-screen {\r\n            font-size: 45px;\r\n            top: 0;\r\n        }\r\n        @include lg-screen {\r\n            top: 0px;\r\n            font-size: 40px;\r\n            left: 5px;\r\n        }\r\n    }\r\n    &__title {\r\n        margin-bottom: 0px;\r\n        font-weight: 600;\r\n        color: hsl(var(--white)/.8);\r\n        letter-spacing: 2px;\r\n    }\r\n    &__usd {\r\n        font-size: 40px;\r\n        background: var(--base-gradient);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        margin-bottom: 0;\r\n        font-weight: 700;\r\n        font-family: var(--heading-font);\r\n        @include lg-screen {\r\n            font-size: 32px;\r\n        }\r\n        @include msm-screen {\r\n            font-size: 30px;\r\n        }\r\n    }\r\n}\r\n/* ===========================investor css end here=========================== */", "/* ======================commition css start here ====================== */\r\n.commition-section{\r\n    position: relative;\r\n    z-index: 1;\r\n    &__inner{\r\n        &-bg {\r\n            -webkit-mask-image: url(../images/shapes/commition.png);\r\n            -webkit-mask-size: 100% 100%;\r\n            mask-size: 100% 100%;\r\n            -webkit-mask-repeat: no-repeat;\r\n            mask-repeat: no-repeat;\r\n            background: var(--base-gradient);\r\n            width: 100%;\r\n            height: 100%;\r\n            position: absolute;\r\n            left: 0;\r\n            z-index: -1;\r\n            top: 50px;\r\n            @include lg-screen {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n    &__shape {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        z-index: -9;\r\n        width: 100%;\r\n        height: 100%;\r\n        @include lg-screen {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n.commition {\r\n    position: relative;\r\n    z-index: 2;\r\n    &__title {\r\n        font-size: var(--heading-one);\r\n    }\r\n   &__thumb {\r\n    margin-left: -100px;\r\n    margin-top: 110px;\r\n    height: 100%;\r\n    position: relative;\r\n    z-index: 2;\r\n    @include lg-screen {\r\n        margin-left: 0;\r\n        margin-top: 0;\r\n    }\r\n    img {\r\n        @extend .fit-image;\r\n    }\r\n   }\r\n   &__desc {\r\n    margin-bottom: 30px;\r\n    font-family: var(--body-font);\r\n    font-weight: 400;\r\n    color: hsl(var(--white));\r\n    @extend .fs-18;\r\n   }\r\n}\r\n/* ======================commition css end here ====================== */", "/* ============================================== policy section css start here ============================================== */\r\n\r\n.policy-left {\r\n        position: sticky;\r\n        top: 100px;\r\n        &__item {\r\n            padding: 10px !important;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: hsl(var(--white)) !important;\r\n            &.active {\r\n                color: hsl(var(--black)) !important;\r\n                background-color: hsl(var(--white)) !important;\r\n            }\r\n            &:last-of-type {\r\n                border-bottom: 0 !important;\r\n            }\r\n        }\r\n        &__wrapper {\r\n            @include sm-screen {\r\n                margin-bottom: 30px;\r\n            }\r\n        }\r\n        &__list {\r\n            margin-bottom: 20px;\r\n            font-size: 24px;\r\n            font-weight: 700;\r\n            color: hsl(var(--white));\r\n            &.active {\r\n                background-color: hsl(var(--white)) !important;\r\n                color: hsl(var(--black)) !important;\r\n            }\r\n        }\r\n}\r\n.policy-card { \r\n    margin-bottom: 50px;\r\n    &__desc {\r\n       margin-bottom: 30px; \r\n       color: hsl(var(--white)/0.8);\r\n       &:last-of-type {\r\n        margin-bottom: 0;\r\n       }\r\n    }\r\n}\r\n\r\n\r\n/*=============================================== policy section css end here =============================================== */", "//================================= blog details css start here =================================\r\n.blog-details {\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background-color: hsl(var(--section-bg));\r\n    // box-shadow: 0px 2px 15px hsl(var(--white) /.25);\r\n    &__thumb {\r\n        height: 450px;\r\n        max-height: 100%;\r\n        @include md-screen {\r\n            height: 350px;\r\n        }\r\n        @include sm-screen {\r\n            height: 300px;\r\n        }\r\n        img {\r\n            @extend .fit-image; \r\n        }\r\n    }\r\n    &__content {\r\n        padding: 35px 25px;\r\n        @include sm-screen {\r\n            padding: 30px 20px;\r\n        }\r\n        @include msm-screen {\r\n            padding: 25px 15px;\r\n        }\r\n    }\r\n    &__title {\r\n        margin-top: 20px;\r\n        margin-bottom: 15px;\r\n    }\r\n    &__desc {\r\n        margin-bottom: 15px;\r\n    }\r\n}\r\n// tag start\r\n.tag {\r\n    @extend .flex-between;\r\n    border-top: 1px solid hsl(var(--white) /.1);\r\n    padding: 20px 40px;\r\n    @include sm-screen {\r\n        padding: 20px 30px;\r\n    }\r\n    @include msm-screen {\r\n        padding: 15px;\r\n    }\r\n    &__title{\r\n       @extend .fs-16;\r\n       margin-bottom: 0;\r\n       font-weight: 400;\r\n       span {\r\n        font-weight: 700;\r\n       }\r\n    }\r\n\r\n}\r\n/* tag end */\r\n\r\n/* investment system css start here */\r\n.investment-system{\r\n    &__item {\r\n      position: relative;\r\n      font-size: 16px;\r\n      font-weight: 400;\r\n      color: hsl(var(--white) /.8);\r\n      padding-left: 30px;\r\n      margin-bottom: 20px;\r\n      &::before {\r\n        position: absolute;\r\n        content: '';\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 50%;\r\n        background: var(--background-gradient);\r\n        left: 0px;\r\n        bottom: 6px;\r\n        line-height: 15px;\r\n      }\r\n    }\r\n}\r\n/*============= contact form css start here============= */\r\n.contact-form {\r\n    margin-top: 50px;\r\n    padding: 30px;\r\n    border-radius: 8px;\r\n    background-color: hsl(var(--section-bg));\r\n}\r\n/* contact form css end here */\r\n// investment system css end here \r\n.social-share {\r\n    &__title {\r\n        @extend .fs-16;\r\n    }\r\n}\r\n/* ================================== blog details css end here ================================== */\r\n\r\n\r\n", "/* ==============Contact top Start============== */\r\n.contact {\r\n    &__bg {\r\n        position: relative;\r\n        background-color: hsl(var(--section-bg));\r\n        border-radius: 60px;\r\n        @include md-screen {\r\n            border-radius: 10px;\r\n        }\r\n        &::before {\r\n            position: absolute;\r\n            content: \"\";\r\n            top: 5px;\r\n            left: -30px;\r\n            width: 100%;\r\n            height: 90%;\r\n            transform: rotate(183deg);\r\n            background: var(--background-gradient);\r\n            z-index: -1;\r\n            border-radius: inherit;\r\n            @include md-screen {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n    &__title {\r\n        margin-bottom: 20px;\r\n        font-size: 30px;\r\n        @include sm-screen {\r\n            font-size: 28px;\r\n        }\r\n    }\r\n    &__desc {\r\n        margin-bottom: 25px;\r\n    }\r\n    &__shape {\r\n        background: var(--base-gradient);\r\n        width: 100%;\r\n        height: 100%;\r\n        -webkit-mask-image: url(../images/shapes/account-shape.png);\r\n        mask-image: url(../images/shapes/account-shape.png);\r\n        -webkit-mask-repeat: no-repeat;\r\n        mask-repeat: no-repeat;\r\n        -webkit-mask-position: center center;\r\n        mask-position: center center;\r\n        -webkit-mask-size: cover;\r\n        mask-size: cover;\r\n        position: absolute;\r\n        bottom: 30px;\r\n        left: -30px;\r\n        z-index: -1;\r\n    }\r\n}\r\n.contact-item {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    background-color: transparent;\r\n    box-shadow: var(--box-shadow);\r\n    border-radius: 5px;\r\n    overflow: hidden;\r\n    height: 100%;\r\n    &__icon {\r\n        width: 110px;\r\n        height: 100px;\r\n        display: flex;\r\n        display: flex;\r\n        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70% ;\r\n        background: var(--background-gradient);\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: hsl(var(--white));\r\n        @include fs-25; \r\n        @include sm-screen {\r\n            width: 70px;\r\n            height: 70px;\r\n            @include fs-18;\r\n        }\r\n    }   \r\n    &__content {\r\n        width: calc(100% - 110px);\r\n        padding-left: 20px;\r\n        @include sm-screen {\r\n            width: calc(100% - 70px);\r\n            padding-left: 15px;\r\n        }\r\n    }\r\n    &__title {\r\n        margin-bottom: 10px;\r\n        color: hsl(var(--white));\r\n    }\r\n    &__desc {\r\n        color: hsl(var(--white)/.6);\r\n    }\r\n}\r\n/*=============== Contact top End ===============*/\r\n\r\n\r\n/*===================== Contact Bottom Start===================== */\r\n.contact-map {\r\n    width: 100%;\r\n    min-height: 300px;\r\n    height: 100%;\r\n    padding: 30px;\r\n    iframe {\r\n        width: 100%;\r\n        height: 100%;\r\n        border-radius: 20px;\r\n\r\n    }\r\n}\r\n\r\n.contactus-form {\r\n    // box-shadow: var(--box-shadow);\r\n    // background-color: hsl(var(--white)/.1);\r\n    padding: 40px 30px;\r\n    @include sm-screen {\r\n        padding: 30px 20px;\r\n    }\r\n    @include msm-screen {\r\n        padding: 20px 15px;\r\n    }\r\n}\r\n/* =====================Contact Bottom End =====================*/\r\n\r\n", "//============================================== account css start here ==============================================\r\n.account {\r\n    padding-top: 150px;\r\n    padding-bottom: 60px;\r\n    @include sm-screen {\r\n        padding-top: 120px\r\n    }\r\n}\r\n.account-wrapper {\r\n    position: relative;\r\n    z-index: 1;\r\n    &::before {\r\n        position: absolute;\r\n        content: \"\";\r\n        top: 5px;\r\n        left: -30px;\r\n        width: 100%;\r\n        height: 90%;\r\n        transform: rotate(183deg);\r\n        background: var(--background-gradient);\r\n        z-index: -1;\r\n        border-radius: 40px;\r\n        @include md-screen {\r\n            display: none;\r\n        }\r\n      }\r\n    &-inner {\r\n      position: relative;\r\n      background-color: hsl(var(--section-bg));\r\n      border-radius: 40px;\r\n      overflow: hidden;\r\n      z-index: 1;\r\n      @include md-screen {\r\n        border-radius: 10px;\r\n      }\r\n    }\r\n}\r\n\r\n\r\n.account-form  {\r\n    box-shadow: 0 6px 20px hsl(var(--black)/.07);\r\n    // background-color: hsl(var(--white)/.1);\r\n    padding: 50px;\r\n    border-radius: 10px;\r\n    overflow: hidden;\r\n    @include lg-screen {\r\n        padding: 40px 30px;\r\n    }\r\n    @include md-screen {\r\n        padding: 35px 25px;\r\n    }\r\n    @include sm-screen {\r\n        padding: 30px 20px;\r\n    }\r\n    @include xsm-screen {\r\n        padding: 30px 15px;\r\n    }\r\n}\r\n\r\n.account-thumb {\r\n    &.style-two {\r\n        @media (min-width:1199px) {\r\n            top: 60px;\r\n        }\r\n    }\r\n    img {\r\n        @extend .fit-image; \r\n    }\r\n    @media (min-width: 1199px) {     \r\n        position: absolute;\r\n        right: 160px;\r\n        bottom: 50px;\r\n        width: 35%;\r\n        img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: contain;\r\n        }\r\n    }\r\n    @include xl-screen {\r\n        right: 100px;\r\n    }\r\n    @include lg-screen {\r\n        display: none;\r\n    }\r\n}\r\n.account-shape-one {\r\n    position: absolute;\r\n    content: \"\";\r\n    top: -270px;\r\n    right: 500px;\r\n    z-index: -1;\r\n    width: 120px;\r\n    animation: horizontal-move 3s ease-in-out infinite;\r\n    animation-delay: 1s;\r\n    @include xl-screen {\r\n        right: 390px;\r\n    }\r\n    @include lg-screen {\r\n        display: none;\r\n    }\r\n}\r\n.account-shape-two {\r\n    position: absolute;\r\n    content: \"\";\r\n    top: -80px;\r\n    right: 60px;\r\n    z-index: -1;\r\n    width: 100px;\r\n    animation: horizontal-move 3s ease-in-out infinite;\r\n    animation-delay: 1s;\r\n    @include lg-screen {\r\n        display: none;\r\n    }\r\n}\r\n.forgot-password {\r\n    &:hover {\r\n        color: hsl(var(--white));\r\n    }\r\n}\r\n.term {\r\n    &:hover {\r\n        color: hsl(var(--white));\r\n    }\r\n}\r\n/*=============================================== account css end here =============================================== */"]}