<?php
/**
 * Test script for Zynte Payment Gateway Integration
 * 
 * This script tests the Zynte API integration without affecting the main application
 * Run this script to verify your Zynte credentials and API connectivity
 */

// Zynte API Configuration (from your zynte.md)
$merchant_id = 'MAM4FNXP5V';
$secret_key = 'cQuEwtz62XxHISzCRwxvBmetStqHKRWL';
$api_base_url = 'https://zynte.in/api/v1';

/**
 * Generate X-VERIFY header for Zynte API
 */
function generateXVerify($data, $secret_key) {
    ksort($data);
    $dataString = implode('|', array_map(function ($key, $value) {
        return $key . '=' . $value;
    }, array_keys($data), $data));
    
    return hash_hmac('sha256', $dataString, $secret_key);
}

/**
 * Make API call to Zynte
 */
function makeApiCall($url, $data, $xverify) {
    $headers = [
        'Content-Type: application/json',
        'X-VERIFY: ' . $xverify,
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        return ['error' => true, 'message' => $error];
    }
    
    curl_close($ch);
    return ['error' => false, 'response' => $response, 'http_code' => $httpCode];
}

echo "=== Zynte Payment Gateway Integration Test ===\n\n";

// Test 1: Create Payment Order
echo "Test 1: Creating Payment Order\n";
echo "--------------------------------\n";

$orderData = [
    'customer_mobile' => '9876543210',
    'merch_id' => $merchant_id,
    'amount' => 100,
    'order_id' => 'TEST_' . time(),
    'currency' => 'INR',
    'redirect_url' => 'https://example.com/success',
    'udf1' => 'Test User',
    'udf2' => '<EMAIL>',
    'udf3' => 'Test Payment',
    'udf4' => 'Investment Platform',
    'udf5' => 'TEST123',
];

// Validate all required parameters are present
echo "Validating required parameters:\n";
$requiredParams = ['customer_mobile', 'merch_id', 'amount', 'order_id', 'currency', 'redirect_url', 'udf1', 'udf2', 'udf3', 'udf4', 'udf5'];
foreach ($requiredParams as $param) {
    if (empty($orderData[$param])) {
        echo "❌ Missing required parameter: $param\n";
    } else {
        echo "✅ $param: " . $orderData[$param] . "\n";
    }
}

$xverify = generateXVerify($orderData, $secret_key);
echo "\nGenerated X-VERIFY: " . $xverify . "\n";

// Show the data string used for X-VERIFY generation
ksort($orderData);
$dataString = implode('|', array_map(function ($key, $value) {
    return $key . '=' . $value;
}, array_keys($orderData), $orderData));
echo "Data string for X-VERIFY: " . $dataString . "\n";

echo "\nSending request to: " . $api_base_url . '/order' . "\n";
echo "Request payload: " . json_encode($orderData, JSON_PRETTY_PRINT) . "\n";

$result = makeApiCall($api_base_url . '/order', $orderData, $xverify);

if ($result['error']) {
    echo "❌ Error: " . $result['message'] . "\n";
} else {
    echo "✅ HTTP Code: " . $result['http_code'] . "\n";
    echo "Response: " . $result['response'] . "\n";
    
    $responseData = json_decode($result['response'], true);
    if (isset($responseData['status']) && $responseData['status'] === true) {
        echo "✅ Order created successfully!\n";
        echo "Order ID: " . $responseData['result']['orderId'] . "\n";
        echo "Payment URL: " . $responseData['result']['payment_url'] . "\n";
        
        $created_order_id = $responseData['result']['orderId'];
        
        // Test 2: Check Order Status
        echo "\n\nTest 2: Checking Order Status\n";
        echo "------------------------------\n";
        
        $statusData = [
            'order_id' => $created_order_id,
            'merch_id' => $merchant_id,
        ];
        
        $statusXverify = generateXVerify($statusData, $secret_key);
        echo "Generated X-VERIFY for status: " . $statusXverify . "\n";
        
        $statusResult = makeApiCall($api_base_url . '/status', $statusData, $statusXverify);
        
        if ($statusResult['error']) {
            echo "❌ Error: " . $statusResult['message'] . "\n";
        } else {
            echo "✅ HTTP Code: " . $statusResult['http_code'] . "\n";
            echo "Response: " . $statusResult['response'] . "\n";
            
            $statusResponseData = json_decode($statusResult['response'], true);
            if (isset($statusResponseData['status']) && $statusResponseData['status'] === true) {
                echo "✅ Status check successful!\n";
                echo "Transaction Status: " . $statusResponseData['result']['txnStatus'] . "\n";
            } else {
                echo "❌ Status check failed: " . ($statusResponseData['message'] ?? 'Unknown error') . "\n";
            }
        }
        
    } else {
        echo "❌ Order creation failed: " . ($responseData['message'] ?? 'Unknown error') . "\n";
    }
}

echo "\n\n=== Integration Test Summary ===\n";
echo "1. Verify that your Zynte credentials are correct\n";
echo "2. Check that your server can connect to zynte.in\n";
echo "3. Ensure the X-VERIFY generation is working properly\n";
echo "4. Test the complete payment flow in your application\n";

echo "\n=== Next Steps ===\n";
echo "1. Run the SQL script: zynte_gateway_setup.sql\n";
echo "2. Configure Zynte gateway in admin panel\n";
echo "3. Test payments with small amounts\n";
echo "4. Set up webhook endpoint for payment notifications\n";

echo "\n=== Files Created ===\n";
echo "✅ ProcessController.php - Main payment processing logic\n";
echo "✅ IPN route added - Webhook handling\n";
echo "✅ Payment views created - User interface\n";
echo "✅ SQL setup script - Database configuration\n";
echo "✅ Test script - Integration verification\n";

?>
