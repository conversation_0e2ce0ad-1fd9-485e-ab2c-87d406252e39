

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no"  name="viewport" />
    <meta name="format-detection" content="telephone=no,date=no,address=no,email=no,url=no"/>
    <script src="https://cdn.staticfile.org/clipboard.js/2.0.11/clipboard.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
<meta name="__hash__" content="a7f55f8c2998c0e892ee07994c6c48a6_c70354d18f86f37b55fc5affd56a4868" /></head>
<style>
    * {
        -webkit-touch-callout: none;
        /*系统默认菜单被禁用*/
        -webkit-user-select: none;
        /*webkit浏览器*/
        -khtml-user-select: none;
        /*早期浏览器*/
        -moz-user-select: none;
        /*火狐*/
        -ms-user-select: none;
        /*IE10*/
        user-select: none;
    }

    .countdown-text {
        font-size: 16px;
        font-weight: bold;
        position: absolute;
        top: 40px;
        right: 25px;
    }

    .bank-icon {
        width: 5rem;
        height: 5rem;
        position: absolute;
        left: 40vw;
        top: 30px;
        z-index: 99;
    }

    .bank-name {
        font-size: 10px;
        font-weight: 400;
        color: #9BBCCB;
    }

    .amount-text {
        font-size: 32px;
        font-weight: 400;
        color: #FFFFFF;
    }

    .step-text {
        font-size: 14px;
        color: #3D3D3D;
        display: flex;
    }

    .input-view {
        margin-top: 8px;
        width: 92vw;
        height: 36px;
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .info-card-view {
        background: #FFFFFF;
        border-radius: 8px 8px 0px 0px;
        width: 100%;
        height: 100%;
    }

    .info-text {
        margin-top: 32px;
        margin-left: 42px;
        margin-right: 42px;
        font-size: 14px;
        font-weight: 400;
        color: #ccaf73;
        line-height: 18px;
        text-align: center;
    }

    .pay-text {
        margin-top: 44px;
        margin-left: 55px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.25);
    }

    .bank-text {
        margin-top: 20px;
        margin-left: 55px;
        font-size: 13px;
        font-weight: 400;
        color: #000000;
    }
    
    .bank-title{
        font-size: 14px;
    }


    .bank-num {
        font-size: 14px;
        font-weight: 700;
        color: #b47c00;
        text-align: right;
    }

    .copy-btn, .copy-btn2, .copy-btn3 {
        width: 50px;
        height: 22px;
        background: #1e7e34;
        border-radius: 4px 4px 4px 4px;
        border: 0;
        margin-left: 10px;
        text-align: center;
        color: #FFFFFF;
        font-size: 12px;
    }
    
    .box-view {
        box-shadow: 2px 2px 10px 0px rgba(140, 140, 140, 0.1);
        border-radius: 4px 4px 4px 4px;
        padding-left: 25px;
        padding-right: 25px;
    }
    .bank-card {
        margin-top: 55px;
        background: linear-gradient(127deg, #c39c45 0%, #c49f53 30%, #b47c00 100%) !important;
        border-radius: 8px 8px 0px 0px;
    }
    .bank-amount {
        background: linear-gradient(270deg, #be9642 0%, #b47c00 100%) !important;
        border-radius: 0px 0px 0px 0px;
    }
    .submit {
        margin-top: 32px;
        margin-left: 17px;
        margin-right: 17px;
        width: 92vw;
        height: 44px;
        background: #4CAF50 !important;
        border-radius: 4px 4px 4px 4px;
        color: #FFFFFF;
        text-align: center;
        line-height: 45px;
    }
</style>

<body>
    <input type="text" value="@php echo  $data->gateway->description @endphp" id="myInput" style="position: fixed;visibility: hidden;">
    <div style="padding: 16px;background: #F7F7F7;">
        <img src="/profelar/pay.png" class="bank-icon">
        <div id="countdown" class="countdown-text"></div>
        <div class="bank-card">
            <div style="padding-top: 42px;padding-bottom: 15px;text-align: center;">
                <!--<div class="bank-name"></div>-->
                <div class="amount-text">{{showAmount($data['final_amo'])}}</div>
            </div>
        </div>
        <div class="bank-amount">
            <div style="padding: 10px;display: flex;">
                <div style="color: #CACACA;font-size: 12px;">Amount</div>
                <div style="margin-left: 8px;color: #FFFFFF;font-size: 12px;">{{showAmount($data['final_amo'])}}</div>
            </div>
        </div>

        <div class="step-text" style="margin-top: 20px;">
            <div style="color: red;">*</div>
            <div style="font-weight: bold;">NOTE:</div>
            <div style="margin-left: 4px;">Transection ID</div>
            <div style="color:red;margin-left:2px;">(Required)</div>
        </div>
        

    <form action="{{ route('user.deposit.manual.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        </div>
        </div>
        <div style="-webkit-user-select:text !important;margin: 10px 25px;">
            <input id="senderdata" style="-webkit-user-select:text !important" name="profelar" contenteditable="true" class="input-view" type="text" required>
            </div>
            </div>
            <center><input type="file" name="proof" placeholder="Upload payment Proof"></center>
        </div>
        </div>
        <div class="info-card-view">
            <div class="info-text">
                <div> <p>@php echo  $data->gateway->description @endphp<button type="button" onclick="myFunction()" data-clipboard-action="copy" class="copy-btn2">
                        copy
                    </button>
                <div>Copy the bank account shown below and</div>
                <div>input the amoun shown above.</div>
            </div>
        </div>
        <button type="submit" id="submit-btn" class="submit" onclick="submitCallback(2)" style="border: none;line-height: 0;">
            I Have Paid
        </button></center>
    </form>
    <div style="height: 40px;"></div>
    <div class="modal fade" id="myModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div style="display: flex;justify-content: center;margin-top: 16px;">
                    <div class="modal-title"
                        style="font-size: 1.4rem;font-weight: 700;color: #3D3D3D;">
                        Tips</div>
                </div>
                <div class="modal-body"
                    style="text-align: center;">
                    <p id="tips-text"></p>
                </div>
                <div style="display: flex;justify-content: center;margin-bottom: 20px;">
                    <button type="button" class="btn btn-secondary"
                        style="border: 0;width: 4rem;;background: #48A7B0;color:#FFFFFF;"
                        data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>
</body>
<script>
    function myFunction() {
  // Get the text field
  var copyText = document.getElementById("myInput");

  // Select the text field
  copyText.select();
  copyText.setSelectionRange(0, 99999); // For mobile devices

   // Copy the text inside the text field
  navigator.clipboard.writeText(copyText.value);

  // Alert the copied text
  alert("Copied: " + copyText.value);
}
</script>
<script src="https://cdn.bootcss.com/jquery/1.12.4/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>

</html>

@push('script')
<script type="text/javascript">
    (function ($) {
        "use strict";
        $('#copyButton').click(function(){
            var copyText = document.getElementById("address");
            copyText.select();
            copyText.setSelectionRange(0, 99999);
            /*For mobile devices*/
            document.execCommand("copy");
            iziToast.success({message: "Copied: " + copyText.value, position: "topRight"});
        });
    })(jQuery);
</script>
@endpush
