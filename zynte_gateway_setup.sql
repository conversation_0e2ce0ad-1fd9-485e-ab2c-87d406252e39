-- SQL script to add <PERSON>yn<PERSON> payment gateway to the system
-- Run this script in your database to add Zynte gateway support

-- Insert Zynte gateway into gateways table
INSERT INTO `gateways` (`id`, `form_id`, `code`, `name`, `alias`, `qr_code_link`, `status`, `gateway_parameters`, `supported_currencies`, `crypto`, `extra`, `description`, `created_at`, `updated_at`) VALUES
(60, 0, 126, 'Zynte', 'Zynte', NULL, 1, '{"merchant_id":{"title":"Merchant ID","global":true,"value":"MAM4FNXP5V"},"secret_key":{"title":"Secret Key","global":true,"value":"cQuEwtz62XxHISzCRwxvBmetStqHKRWL"}}', '{"INR":"INR","USD":"USD","EUR":"EUR","GBP":"GBP","AUD":"AUD","CAD":"CAD","SGD":"SGD","JPY":"JPY","CNY":"CNY","HKD":"HKD"}', 0, '{"webhook_url":{"title":"Webhook URL","value":"ipn.Zynte"}}', 'Zynte Payment Gateway - Indian payment processor supporting multiple currencies', NOW(), NOW());

-- Insert sample gateway currency for Zynte (INR)
INSERT INTO `gateway_currencies` (`id`, `name`, `currency`, `symbol`, `method_code`, `gateway_alias`, `qr_code_link`, `min_amount`, `max_amount`, `percent_charge`, `fixed_charge`, `rate`, `image`, `gateway_parameter`, `created_at`, `updated_at`) VALUES
(NULL, 'Zynte - INR', 'INR', '₹', 126, 'Zynte', NULL, 10.00000000, 100000.00000000, 2.50, 0.00000000, 1.00000000, NULL, '{"merchant_id":"MAM4FNXP5V","secret_key":"cQuEwtz62XxHISzCRwxvBmetStqHKRWL"}', NOW(), NOW());

-- Insert sample gateway currency for Zynte (USD)
INSERT INTO `gateway_currencies` (`id`, `name`, `currency`, `symbol`, `method_code`, `gateway_alias`, `qr_code_link`, `min_amount`, `max_amount`, `percent_charge`, `fixed_charge`, `rate`, `image`, `gateway_parameter`, `created_at`, `updated_at`) VALUES
(NULL, 'Zynte - USD', 'USD', '$', 126, 'Zynte', NULL, 1.00000000, 10000.00000000, 2.50, 0.00000000, 82.50000000, NULL, '{"merchant_id":"MAM4FNXP5V","secret_key":"cQuEwtz62XxHISzCRwxvBmetStqHKRWL"}', NOW(), NOW());

-- Note: 
-- 1. The merchant_id and secret_key values above are from your zynte.md documentation
-- 2. You should replace these with your actual Zynte credentials
-- 3. The rate for USD is set to 82.50 (approximate USD to INR conversion rate)
-- 4. You can adjust min_amount, max_amount, percent_charge, and fixed_charge as needed
-- 5. The gateway code 126 should be unique - adjust if needed
