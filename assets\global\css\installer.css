/*
Easy Installer by Viser<PERSON>ab
Laravel Software Setup Module By viserlab.com
*/

@import url(https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap);

html {
    font-size: 18px;
}

body {
    color: #ddd;
    line-height: 26px;
    font-family: "Courier Prime", sans-serif;
    background: #050d3d;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

footer {
    margin-top: auto;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700;
    margin: 0;
    line-height: 1.3;
    color: #fff;
}

h2 {
    font-size: 36px;
}

h3 {
    font-size: 28px;
}

h4 {
    font-size: 24px;
}

h5 {
    font-size: 20px;
}

h6 {
    font-size: 18px;
}

@media (min-width: 768px) {
    h2 {
        font-size: 48px;
        line-height: 50px;
    }

    h3 {
        font-size: 36px;
    }

    h4 {
        font-size: 30px;
    }

    h5 {
        font-size: 24px;
    }

    h6 {
        font-size: 22px;
    }
}

@media (max-width: 575px) {
    h2 {
        font-size: 42px;
        line-height: 50px;
    }

    h3 {
        font-size: 22px;
    }

    h4 {
        font-size: 20px;
    }

    h5 {
        font-size: 18px;
    }

    h6 {
        font-size: 16px;
    }
}

@media (max-width:450px) {
    h3 {
        font-size: 18px;
    }
}

ul {
    margin: 0;
    padding: 0;
}

ul li {
    list-style: none;
}

.padding-top {
    padding-top: 100px;
}

.padding-bottom {
    padding-bottom: 100px;
}

@media (max-width:991px) {
    .padding-bottom {
        padding-bottom: 70px;
    }

    .padding-top {
        padding-top: 70px;
    }
}

.installation-wrapper {
    background: #071251;
    border-radius: 10px;
    margin-bottom: 40px;
}

.installation-wrapper:last-child {
    margin-bottom: 0;
}

@media (max-width: 991px) {
    .installation-wrapper {
        padding: 40px 30px;
    }
}

@media (max-width: 767px) {
    .installation-wrapper {
        padding: 0;
    }
}

.box-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 0 0 15px 15px;
}

@media (max-width: 575px) {
    .box-item {
        padding: 30px 15px;
    }
}

.install-item .title {
    padding: 16px 0 12px 0;
    border-radius: 10px 10px 0 0;
    background: #0087ff;
    text-transform: uppercase;
}

.install-item .item {
    margin-bottom: 50px;
}

.install-item .item:last-child {
    margin-bottom: 0;
}

.install-item .item .subtitle {
    font-weight: 400;
    margin-bottom: 20px;
}

.install-item .item p {
    margin: 0;
    margin-bottom: 20px;
}

.install-item .item .check-list {
    margin-bottom: 25px;
}

.install-item .item .check-list:last-child {
    margin-bottom: 0 !important;
}

.install-item .item .check-list li:first-child {
    padding-top: 0;
}

.install-item .item .check-list li:last-child {
    padding-bottom: 0;
}

.install-item .item .check-list li::before {
    content: "\f00c";
    color: #28C76F;
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    font-size: 14px;
    margin-right: 5px;
}

.install-item .item .check-list li.no::before {
    color: #ff4111;
    content: "\f00d";
}

.install-item .item .info {
    margin-bottom: 25px;
}

.install-item .item .info:last-child {
    margin-bottom: 0 !important;
}

.install-item .item .info img {
    width: 20px;
    margin-right: 5px;
}

.install-item .item .info a {
    margin: 0 5px;
}

@media (max-width: 575px) {
    .install-item .item {
        margin-bottom: 40px;
    }

    .install-item .item .subtitle {
        margin-bottom: 15px;
    }

    .install-item .item .check-list,
    .install-item .item .info,
    .install-item .item p {
        margin-bottom: 20px;
    }
}

.requirment-table {
    width: 100%;
}

.requirment-table tr {
    border-left: 1px solid rgba(255, 255, 255, 0.12);
    border-right: 1px solid rgba(255, 255, 255, 0.12);
}

.requirment-table tr:first-child {
    border-top: 1px solid rgba(255, 255, 255, 0.12);
}

.requirment-table tr td {
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

@media (max-width:767px) {
    .requirment-table tr td {
        font-size: 16px;
        ;
    }
}

@media (max-width:575px) {
    .requirment-table tr td {
        font-size: 15px;
        ;
    }
}

.requirment-table tr td:last-child {
    text-align: center;
}

.requirment-table tr td i {
    color: #e84118;
}

.requirment-table tr td i[class*="check"] {
    color: #28C76F;
}

.requirment-table tr:nth-child(even) {
    background: #050d3d;
}

.table-area.item {
    margin-bottom: 30px;
}

@media (max-width: 767px) {
    .table-area {
        overflow-x: auto;
    }

    .table-area .requirment-table {
        width: 700px;
    }
}

.info-item {
    margin-bottom: 40px;
}

.info-item:last-child {
    margin-bottom: 0 !important;
}

.info-item .subtitle {
    text-transform: uppercase;
    font-weight: 400;
    font-size: 18px;
    margin-bottom: 10px;
}

@media (max-width: 575px) {
    .info-item {
        margin-bottom: 30px;
    }

    p {
        font-size: 16px;
    }

    .check-list {
        font-size: 16px;
    }
}

.mb--20 {
    margin-bottom: -20px;
}

.information-form-group {
    margin-bottom: 20px;
}

.success-area {
    padding: 30px 0;
}

.success-area .subtitle {
    text-transform: uppercase;
    margin-bottom: 70px;
}

.success-area .info {
    display: block;
    color: #28C76F;
    margin-bottom: 30px;
}

.success-area .warning {
    margin-top: 70px;
}

@media (max-width: 575px) {
    .success-area .subtitle {
        margin-bottom: 40px;
    }

    .success-area .info {
        margin-bottom: 20px;
    }

    .success-area .warning {
        margin-top: 40px;
    }
}

.logo {
    max-width: 180px;
    object-fit: contain;
}

@media (max-width:575px) {
    .logo {
        max-width: 140px;
    }
}

@media (max-width:450px) {
    .logo {
        max-width: 115px;
    }
}

a {
    display: inline-block;
}

.theme-button {
    font-weight: 700;
    border-radius: 5px;
    line-height: 28px;
    padding: 15px 30px;
    text-transform: uppercase;
    color: #fff;
    background: #0087ff;
    position: relative;
    z-index: 1;
}

.theme-button::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: #28C76F;
    transition: all 0.45s;
    z-index: -1;
    border-radius: 5px;
}

.theme-button:hover::before {
    width: 100%;
}

.theme-button i:last-child {
    margin-left: 7px;
}

.theme-button i:first-child {
    margin-right: 7px;
}

.theme-button:hover {
    color: #fff;
    text-decoration: none;
}

.theme-button.choto {
    padding: 10px 30px;
}

button.theme-button {
    border: none;
}

@media (max-width:575px) {
    .theme-button {
        font-size: 15px;
    }
}

@keyframes outer-ripple {
    0% {
        transform: scale(1);
        opacity: 0.5;
        -webkit-transform: scale(1);
    }

    80% {
        transform: scale(1.5);
        opacity: 0;
        -webkit-transform: scale(1.5);
    }

    100% {
        transform: scale(2.5);
        opacity: 0;
        -webkit-transform: scale(2.5);
    }
}

@keyframes inner-ripple {
    0% {
        transform: scale(1);
        opacity: 0.5;
        -webkit-transform: scale(1);
    }

    30% {
        transform: scale(1);
        opacity: 0.5;
        -webkit-transform: scale(1);
    }

    100% {
        transform: scale(1.5);
        opacity: 0;
        -webkit-transform: scale(1.5);
    }
}

a {
    text-decoration: none;
}

.bg--dark {
    background-color: #1d1d1e !important;
}

.passed-btn {
    background: transparent;
    color: #28c76f;
    border-color: #28c76f;
    font-weight: 500;
    line-height: 1;
    padding-top: 7px;
}

.passed-btn:hover {
    color: #000;
    background-color: #28c76f;
    border-color: #28c76f;
}

.passed-btn:focus {
    box-shadow: none;
}


input:not([type=radio]) {
    border-radius: 5px;
    color: #fff;
}

input:not([type=radio]) ::placeholder {
    color: rgb(185, 185, 185);
}

input:not([type=radio]) {
    width: 100%;
    height: 50px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: #050f48;
    padding: 0 15px;
    outline: 0 !important;
}

input:focus {
    border-color: #0087ff !important;
}

input::placeholder {
    color: #ddd;
}

.text-success {
    color: #28C76F !important;
}

.text-danger {
    color: #ff4111 !important;
}

.icon.big {
    font-size: 76px;
}

.success-icon {
    font-size: 70px;
}

.font-small {
    font-size: 15px
}

.alert-primary {
    color: #1e74f5;
    background-color: #08429830;
    border-color: #084298;
}

.alert-warning {
    color: #af8406;
    background-color: #a77e0730;
    border-color: #664d03;
}

.alert-success {
    color: #1a955b;
    background-color: #13764857;
    border-color: #0f5132;
}

.word-break-all {
    word-break: break-all;
}

.fs-17 {
    font-size: 17px;
}

.agree-label {
    margin-left: 5px;
}

.p-code-info {
    cursor: pointer;
}



.alert-area {
    text-align: center;
    margin-bottom: 30px;
}

.alert-danger {
    color: #d90e1f;
    background-color: #d90e1f29;
    border-color: #d90e1f;
}

.alert-area h5 {
    color: #d90e1f;
}

.alert-area a {
    line-height: 1;
    padding-top: 7px;
    text-transform: uppercase;
}


/* secure password */
.hover-input-popup {
    position: relative;
}

.input-popup {
    display: none;
}

.hover-input-popup .input-popup {
    display: block;
    position: absolute;
    bottom: 100%;
    left: 50%;
    width: 280px;
    background-color: #1a1a1a;
    color: #fff;
    padding: 10px;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    z-index: 99999;
}

.hover-input-popup .input-popup p {
    margin-bottom: 0;
}

.input-popup::after {
    position: absolute;
    content: '';
    bottom: -19px;
    left: 50%;
    margin-left: -5px;
    border-width: 10px 10px 10px 10px;
    border-style: solid;
    border-color: transparent transparent #1a1a1a transparent;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.input-popup p {
    padding-left: 20px;
    position: relative;
}

.input-popup p::before {
    position: absolute;
    content: '';
    font-family: 'Line Awesome Free';
    font-weight: 900;
    left: 0;
    top: 4px;
    line-height: 1;
    font-size: 18px;
}

.input-popup p.error {
    text-decoration: line-through;
}

.input-popup p.error::before {
    content: "\2716";
    color: #ea5455;
}

.input-popup p.success::before {
    content: "\2714";
    color: #28c76f;
}

.input-popup p.success,
.input-popup p.error {
    font-size: 14px;
}

.weak-password-error::before {
    content: "\2716";
}

.review-alert{
    padding: 0 200px;
}

@media (max-width: 1399px) {
    .review-alert{
        padding: 0 150px;
    }
}
@media (max-width: 1199px) {
    .review-alert{
        padding: 0 100px;
    }
}
@media (max-width: 991px) {
    .review-alert{
        padding: 0 40px;
    }
}
@media (max-width: 991px) {
    .review-alert{
        padding: 0 10px;
    }
}