<div class="sidebar bg--dark">
    <button class="res-sidebar-close-btn"><i class="las la-times"></i></button>
    <div class="sidebar__inner">
        <div class="sidebar__logo">
            <a href="{{ route('admin.dashboard') }}" class="sidebar__main-logo"><img src="data:image/png;base64,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" alt="@lang('image')"></a>
        </div>

        <div class="sidebar__menu-wrapper" id="sidebar__menuWrapper">
            <ul class="sidebar__menu">
                <li class="sidebar-menu-item {{ menuActive('admin.dashboard') }}">
                    <a href="{{ route('admin.dashboard') }}" class="nav-link ">
                        <i class="menu-icon las la-home"></i>
                        <span class="menu-title">@lang('Dashboard')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.invest.report.dashboard') }}">
                    <a href="{{ route('admin.invest.report.dashboard') }}" class="nav-link ">
                        <i class="menu-icon las la-signal"></i>
                        <span class="menu-title">@lang('Investment Report')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.deposit*', 3) }}">
                        <i class="menu-icon las la-file-invoice-dollar"></i>
                        <span class="menu-title">@lang('Deposits')</span>
                        @if (0 < $pendingDepositsCount)
                            <span class="menu-badge pill bg--danger ms-auto">
                                <i class="fa fa-exclamation"></i>
                            </span>
                        @endif
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.deposit*', 2) }} ">
                        <ul>

                            <li class="sidebar-menu-item {{ menuActive('admin.deposit.pending') }} ">
                                <a href="{{ route('admin.deposit.pending') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Pending Deposits')</span>
                                    @if ($pendingDepositsCount)
                                        <span class="menu-badge pill bg--danger ms-auto">{{ $pendingDepositsCount }}</span>
                                    @endif
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.deposit.approved') }} ">
                                <a href="{{ route('admin.deposit.approved') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Approved Deposits')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.deposit.successful') }} ">
                                <a href="{{ route('admin.deposit.successful') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Successful Deposits')</span>
                                </a>
                            </li>


                            <li class="sidebar-menu-item {{ menuActive('admin.deposit.rejected') }} ">
                                <a href="{{ route('admin.deposit.rejected') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Rejected Deposits')</span>
                                </a>
                            </li>


                            <li class="sidebar-menu-item {{ menuActive('admin.deposit.initiated') }} ">

                                <a href="{{ route('admin.deposit.initiated') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Initiated Deposits')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.deposit.list') }} ">
                                <a href="{{ route('admin.deposit.list') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('All Deposits')</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.withdraw*', 3) }}">
                        <i class="menu-icon la la-bank"></i>
                        <span class="menu-title">@lang('Withdrawals') </span>
                        @if (0 < $pendingWithdrawCount)
                            <span class="menu-badge pill bg--danger ms-auto">
                                <i class="fa fa-exclamation"></i>
                            </span>
                        @endif
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.withdraw*', 2) }} ">
                        <ul>

                            <li class="sidebar-menu-item {{ menuActive('admin.withdraw.method.*') }}">
                                <a href="{{ route('admin.withdraw.method.index') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Withdrawal Methods')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.withdraw.pending') }} ">
                                <a href="{{ route('admin.withdraw.pending') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Pending Withdrawals')</span>

                                    @if ($pendingWithdrawCount)
                                        <span class="menu-badge pill bg--danger ms-auto">{{ $pendingWithdrawCount }}</span>
                                    @endif
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.withdraw.approved') }} ">
                                <a href="{{ route('admin.withdraw.approved') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Approved Withdrawals')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.withdraw.rejected') }} ">
                                <a href="{{ route('admin.withdraw.rejected') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Rejected Withdrawals')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.withdraw.log') }} ">
                                <a href="{{ route('admin.withdraw.log') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('All Withdrawals')</span>
                                </a>
                            </li>


                        </ul>
                    </div>
                </li>

                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.users*', 3) }}">
                        <i class="menu-icon las la-users"></i>
                        <span class="menu-title">@lang('Manage Users')</span>

                        @if ($bannedUsersCount > 0 || $emailUnverifiedUsersCount > 0 || $mobileUnverifiedUsersCount > 0 || $kycUnverifiedUsersCount > 0 || $kycPendingUsersCount > 0)
                            <span class="menu-badge pill bg--danger ms-auto">
                                <i class="fa fa-exclamation"></i>
                            </span>
                        @endif
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.users*', 2) }} ">
                        <ul>
                            <li class="sidebar-menu-item {{ menuActive('admin.users.active') }} ">
                                <a href="{{ route('admin.users.active') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Active Users')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.users.banned') }} ">
                                <a href="{{ route('admin.users.banned') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Banned Users')</span>
                                    @if ($bannedUsersCount)
                                        <span class="menu-badge pill bg--danger ms-auto">{{ $bannedUsersCount }}</span>
                                    @endif
                                </a>
                            </li>

                            <li class="sidebar-menu-item  {{ menuActive('admin.users.email.unverified') }}">
                                <a href="{{ route('admin.users.email.unverified') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Email Unverified')</span>

                                    @if ($emailUnverifiedUsersCount)
                                        <span class="menu-badge pill bg--danger ms-auto">{{ $emailUnverifiedUsersCount }}</span>
                                    @endif
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.users.mobile.unverified') }}">
                                <a href="{{ route('admin.users.mobile.unverified') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Mobile Unverified')</span>
                                    @if ($mobileUnverifiedUsersCount)
                                        <span class="menu-badge pill bg--danger ms-auto">{{ $mobileUnverifiedUsersCount }}</span>
                                    @endif
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.users.kyc.unverified') }}">
                                <a href="{{ route('admin.users.kyc.unverified') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('KYC Unverified')</span>
                                    @if ($kycUnverifiedUsersCount)
                                        <span class="menu-badge pill bg--danger ms-auto">{{ $kycUnverifiedUsersCount }}</span>
                                    @endif
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.users.kyc.pending') }}">
                                <a href="{{ route('admin.users.kyc.pending') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('KYC Pending')</span>
                                    @if ($kycPendingUsersCount)
                                        <span class="menu-badge pill bg--danger ms-auto">{{ $kycPendingUsersCount }}</span>
                                    @endif
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.users.with.balance') }}">
                                <a href="{{ route('admin.users.with.balance') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('With Balance')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.users.all') }} ">
                                <a href="{{ route('admin.users.all') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('All Users')</span>
                                </a>
                            </li>


                            <li class="sidebar-menu-item {{ menuActive('admin.users.notification.all') }}">
                                <a href="{{ route('admin.users.notification.all') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Notification to All')</span>
                                </a>
                            </li>

                        </ul>
                    </div>
                </li>

                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.ticket*', 3) }}">
                        <i class="menu-icon la la-ticket"></i>
                        <span class="menu-title">@lang('Support Ticket') </span>
                        @if (0 < $pendingTicketCount)
                            <span class="menu-badge pill bg--danger ms-auto">
                                <i class="fa fa-exclamation"></i>
                            </span>
                        @endif
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.ticket*', 2) }} ">
                        <ul>
                            <li class="sidebar-menu-item {{ menuActive('admin.ticket.pending') }} ">
                                <a href="{{ route('admin.ticket.pending') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Pending Ticket')</span>
                                    @if ($pendingTicketCount)
                                        <span class="menu-badge pill bg--danger ms-auto">{{ $pendingTicketCount }}</span>
                                    @endif
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.ticket.closed') }} ">
                                <a href="{{ route('admin.ticket.closed') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Closed Ticket')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.ticket.answered') }} ">
                                <a href="{{ route('admin.ticket.answered') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Answered Ticket')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.ticket.index') }} ">
                                <a href="{{ route('admin.ticket.index') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('All Ticket')</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.report*', 3) }}">
                        <i class="menu-icon la la-list"></i>
                        <span class="menu-title">@lang('Report') </span>
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.report*', 2) }} ">
                        <ul>
                            <li class="sidebar-menu-item {{ menuActive(['admin.report.transaction', 'admin.report.transaction.search']) }}">
                                <a href="{{ route('admin.report.transaction') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Transaction Log')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive(['admin.report.login.history', 'admin.report.login.ipHistory']) }}">
                                <a href="{{ route('admin.report.login.history') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Login History')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.report.notification.history') }}">
                                <a href="{{ route('admin.report.notification.history') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Notification History')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.report.invest*') }}">
                                <a href="{{ route('admin.report.invest.history') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Invest History')</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>


                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive(['admin.time.index*', 'admin.plan.index*'], 3) }}">
                        <i class="menu-icon las la-clipboard-check"></i>
                        <span class="menu-title">@lang('Plan Manage')</span>
                    </a>
                    <div class="sidebar-submenu {{ menuActive(['admin.time.index*', 'admin.plan.index*'], 2) }} ">
                        <ul>
                            <li class="sidebar-menu-item {{ menuActive('admin.time.index') }} ">
                                <a href="{{ route('admin.time.index') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Time Manage')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.plan.index') }}">
                                <a href="{{ route('admin.plan.index') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Plan Manage')</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                @if ($general->staking_option)
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="{{ menuActive('admin.staking*', 3) }}">
                            <i class="menu-icon las la-chart-line"></i>
                            <span class="menu-title">@lang('Manage Staking')</span>
                        </a>
                        <div class="sidebar-submenu {{ menuActive('admin.staking*', 2) }} ">
                            <ul>
                                <li class="sidebar-menu-item {{ menuActive('admin.staking.index') }}">
                                    <a href="{{ route('admin.staking.index') }}" class="nav-link">
                                        <i class="menu-icon las la-dot-circle"></i>
                                        <span class="menu-title">@lang('Plan')</span>
                                    </a>
                                </li>
                                <li class="sidebar-menu-item {{ menuActive('admin.staking.invest') }} ">
                                    <a href="{{ route('admin.staking.invest') }}" class="nav-link">
                                        <i class="menu-icon las la-dot-circle"></i>
                                        <span class="menu-title">@lang('Staking Invest')</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                @endif

                @if ($general->pool_option)
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="{{ menuActive('admin.pool*', 3) }}">
                            <i class="menu-icon las la-cubes"></i>
                            <span class="menu-title">@lang('Manage Pool')</span>
                        </a>
                        <div class="sidebar-submenu {{ menuActive('admin.pool*', 2) }} ">
                            <ul>
                                <li class="sidebar-menu-item {{ menuActive('admin.pool.index') }}">
                                    <a href="{{ route('admin.pool.index') }}" class="nav-link">
                                        <i class="menu-icon las la-dot-circle"></i>
                                        <span class="menu-title">@lang('Plan')</span>
                                    </a>
                                </li>
                                <li class="sidebar-menu-item {{ menuActive('admin.pool.invest') }} ">
                                    <a href="{{ route('admin.pool.invest') }}" class="nav-link">
                                        <i class="menu-icon las la-dot-circle"></i>
                                        <span class="menu-title">@lang('Pool Invest')</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                @endif


                <li class="sidebar-menu-item {{ menuActive('admin.referrals.index') }}">
                    <a href="{{ route('admin.referrals.index') }}" class="nav-link ">
                        <i class="menu-icon las la-tree"></i>
                        <span class="menu-title">@lang('Manage Referral')</span>
                    </a>
                </li>

                @if ($general->user_ranking)
                    <li class="sidebar-menu-item {{ menuActive('admin.ranking.list') }}">
                        <a href="{{ route('admin.ranking.list') }}" class="nav-link ">
                            <i class="menu-icon las la-medal"></i>
                            <span class="menu-title">@lang('User Ranking')</span>
                        </a>
                    </li>
                @endif

                <li class="sidebar-menu-item {{ menuActive('admin.promotional.tool.index') }}">
                    <a href="{{ route('admin.promotional.tool.index') }}" class="nav-link ">
                        <i class="menu-icon las la-ad"></i>
                        <span class="menu-title">@lang('Promotional Tool')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.gateway*', 3) }}">
                        <i class="menu-icon las la-credit-card"></i>
                        <span class="menu-title">@lang('Payment Gateways')</span>
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.gateway*', 2) }} ">
                        <ul>
                            <li class="sidebar-menu-item {{ menuActive('admin.gateway.automatic.*') }} ">
                                <a href="{{ route('admin.gateway.automatic.index') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Automatic Gateways')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.gateway.manual.*') }} ">
                                <a href="{{ route('admin.gateway.manual.index') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Manual Gateways')</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="sidebar-menu-item  {{ menuActive('admin.subscriber.*') }}">
                    <a href="{{ route('admin.subscriber.index') }}" class="nav-link" data-default-url="{{ route('admin.subscriber.index') }}">
                        <i class="menu-icon las la-thumbs-up"></i>
                        <span class="menu-title">@lang('Subscribers') </span>
                    </a>
                </li>

                <li class="sidebar__menu-header">@lang('Settings')</li>

                <li class="sidebar-menu-item {{ menuActive('admin.setting.index') }}">
                    <a href="{{ route('admin.setting.index') }}" class="nav-link">
                        <i class="menu-icon las la-life-ring"></i>
                        <span class="menu-title">@lang('General Setting')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.setting.system.configuration') }}">
                    <a href="{{ route('admin.setting.system.configuration') }}" class="nav-link">
                        <i class="menu-icon las la-cog"></i>
                        <span class="menu-title">@lang('System Configuration')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.cron*') }}">
                    <a href="{{ route('admin.cron.index') }}" class="nav-link">
                        <i class="menu-icon las la-clock"></i>
                        <span class="menu-title">@lang('Cron Job Setting')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.setting.logo.icon') }}">
                    <a href="{{ route('admin.setting.logo.icon') }}" class="nav-link">
                        <i class="menu-icon las la-images"></i>
                        <span class="menu-title">@lang('Logo & Favicon')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.extensions.index') }}">
                    <a href="{{ route('admin.extensions.index') }}" class="nav-link">
                        <i class="menu-icon las la-cogs"></i>
                        <span class="menu-title">@lang('Extensions')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item  {{ menuActive(['admin.language.manage', 'admin.language.key']) }}">
                    <a href="{{ route('admin.language.manage') }}" class="nav-link" data-default-url="{{ route('admin.language.manage') }}">
                        <i class="menu-icon las la-language"></i>
                        <span class="menu-title">@lang('Language') </span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.seo') }}">
                    <a href="{{ route('admin.seo') }}" class="nav-link">
                        <i class="menu-icon las la-globe"></i>
                        <span class="menu-title">@lang('SEO Manager')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.kyc.setting') }}">
                    <a href="{{ route('admin.kyc.setting') }}" class="nav-link">
                        <i class="menu-icon las la-user-check"></i>
                        <span class="menu-title">@lang('KYC Setting')</span>
                    </a>
                </li>


                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.setting.notification*', 3) }}">
                        <i class="menu-icon las la-bell"></i>
                        <span class="menu-title">@lang('Notification Setting')</span>
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.setting.notification*', 2) }} ">
                        <ul>
                            <li class="sidebar-menu-item {{ menuActive('admin.setting.notification.global') }} ">
                                <a href="{{ route('admin.setting.notification.global') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Global Template')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.setting.notification.email') }} ">
                                <a href="{{ route('admin.setting.notification.email') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Email Setting')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.setting.notification.sms') }} ">
                                <a href="{{ route('admin.setting.notification.sms') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('SMS Setting')</span>
                                </a>
                            </li>

                            <li class="sidebar-menu-item {{ menuActive('admin.setting.notification.push') }} ">
                                <a href="{{ route('admin.setting.notification.push') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Push Notification Setting')</span>
                                </a>
                            </li>


                            <li class="sidebar-menu-item {{ menuActive('admin.setting.notification.templates') }} ">
                                <a href="{{ route('admin.setting.notification.templates') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Notification Templates')</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.setting.holiday') }}">
                    <a href="{{ route('admin.setting.holiday') }}" class="nav-link">
                        <i class="menu-icon la la-toggle-off"></i>
                        <span class="menu-title">@lang('Holiday Setting')</span>
                    </a>
                </li>

                <li class="sidebar__menu-header">@lang('Frontend Manager')</li>

                <li class="sidebar-menu-item {{ menuActive('admin.frontend.templates') }}">
                    <a href="{{ route('admin.frontend.templates') }}" class="nav-link ">
                        <i class="menu-icon la la-puzzle-piece"></i>
                        <span class="menu-title">@lang('Manage Templates')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.frontend.manage.*') }}">
                    <a href="{{ route('admin.frontend.manage.pages') }}" class="nav-link ">
                        <i class="menu-icon la la-list"></i>
                        <span class="menu-title">@lang('Manage Pages')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.frontend.sections*', 3) }}">
                        <i class="menu-icon la la-html5"></i>
                        <span class="menu-title">@lang('Manage Section')</span>
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.frontend.sections*', 2) }} ">
                        <ul>
                            @php
                                $lastSegment = collect(request()->segments())->last();
                            @endphp
                            @foreach (getPageSections(true) as $k => $secs)
                                @if ($secs['builder'])
                                    <li class="sidebar-menu-item  @if ($lastSegment == $k) active @endif ">
                                        <a href="{{ route('admin.frontend.sections', $k) }}" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title">{{ __($secs['name']) }}</span>
                                        </a>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                </li>

                <li class="sidebar__menu-header">@lang('Extra')</li>


                <li class="sidebar-menu-item {{ menuActive('admin.maintenance.mode') }}">
                    <a href="{{ route('admin.maintenance.mode') }}" class="nav-link">
                        <i class="menu-icon las la-robot"></i>
                        <span class="menu-title">@lang('Maintenance Mode')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.setting.cookie') }}">
                    <a href="{{ route('admin.setting.cookie') }}" class="nav-link">
                        <i class="menu-icon las la-cookie-bite"></i>
                        <span class="menu-title">@lang('GDPR Cookie')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item sidebar-dropdown">
                    <a href="javascript:void(0)" class="{{ menuActive('admin.system*', 3) }}">
                        <i class="menu-icon la la-server"></i>
                        <span class="menu-title">@lang('System')</span>
                    </a>
                    <div class="sidebar-submenu {{ menuActive('admin.system*', 2) }} ">
                        <ul>
                            <li class="sidebar-menu-item {{ menuActive('admin.system.info') }} ">
                                <a href="{{ route('admin.system.info') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Application')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.system.server.info') }} ">
                                <a href="{{ route('admin.system.server.info') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Server')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.system.optimize') }} ">
                                <a href="{{ route('admin.system.optimize') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Cache')</span>
                                </a>
                            </li>
                            <li class="sidebar-menu-item {{ menuActive('admin.system.update') }} ">
                                <a href="{{ route('admin.system.update') }}" class="nav-link">
                                    <i class="menu-icon las la-dot-circle"></i>
                                    <span class="menu-title">@lang('Update')</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="sidebar-menu-item {{ menuActive('admin.setting.custom.css') }}">
                    <a href="{{ route('admin.setting.custom.css') }}" class="nav-link">
                        <i class="menu-icon lab la-css3-alt"></i>
                        <span class="menu-title">@lang('Custom CSS')</span>
                    </a>
                </li>

                <li class="sidebar-menu-item  {{ menuActive('admin.request.report') }}">
                    <a href="{{ route('admin.request.report') }}" class="nav-link" data-default-url="{{ route('admin.request.report') }}">
                        <i class="menu-icon las la-bug"></i>
                        <span class="menu-title">@lang('Report & Request') </span>
                    </a>
                </li>
            </ul>
            <div class="text-center mb-3 text-uppercase">
                <span class="text--primary">{{ __(systemDetails()['name']) }}</span>
                <span class="text--success">@lang('V'){{ systemDetails()['version'] }} </span>
            </div>
        </div>
    </div>
</div>
<!-- sidebar end -->

@push('script')
    <script>
        if ($('li').hasClass('active')) {
            $('#sidebar__menuWrapper').animate({
                scrollTop: eval($(".active").offset().top - 320)
            }, 500);
        }
    </script>
@endpush
